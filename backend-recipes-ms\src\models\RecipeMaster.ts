import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

interface RecipeMasterAttributes {
  id?: number;
  master_recipe_slug: string;
  master_recipe_title: string;
  organization_id?: string;
  current_published_version_id?: number;
  current_draft_version_id?: number;
  total_versions: number;
  is_active: boolean;
  created_by: number;
  updated_by: number;
  created_at?: Date;
  updated_at?: Date;
}

export class RecipeMaster
  extends Model<RecipeMasterAttributes, never>
  implements RecipeMasterAttributes {
  id!: number;
  master_recipe_slug!: string;
  master_recipe_title!: string;
  organization_id?: string;
  current_published_version_id?: number;
  current_draft_version_id?: number;
  total_versions!: number;
  is_active!: boolean;
  created_by!: number;
  updated_by!: number;
  created_at!: Date;
  updated_at!: Date;
}

RecipeMaster.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    master_recipe_slug: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      comment: 'Unique slug for the master recipe across all versions',
    },
    master_recipe_title: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: 'Master title for the recipe (used for identification)',
    },
    organization_id: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: 'Organization ID for multi-tenant support',
    },
    current_published_version_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Reference to the currently published version',
    },
    current_draft_version_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Reference to the current draft version (if any)',
    },
    total_versions: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Total number of versions created for this recipe',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Whether this master recipe is active',
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: 'User ID who created the master recipe',
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: 'User ID who last updated the master recipe',
    },
  },
  {
    sequelize,
    tableName: "mo_recipe_master",
    modelName: "RecipeMaster",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
  }
);

// Define associations
RecipeMaster.associate = (models: any) => {
  // RecipeMaster has many Recipe versions
  RecipeMaster.hasMany(models.Recipe, {
    foreignKey: "master_recipe_id",
    as: "versions",
  });

  // RecipeMaster belongs to current published version
  RecipeMaster.belongsTo(models.Recipe, {
    foreignKey: "current_published_version_id",
    as: "currentPublishedVersion",
    constraints: false,
  });

  // RecipeMaster belongs to current draft version
  RecipeMaster.belongsTo(models.Recipe, {
    foreignKey: "current_draft_version_id",
    as: "currentDraftVersion",
    constraints: false,
  });

  // RecipeMaster belongs to User (created_by)
  RecipeMaster.belongsTo(models.User, {
    foreignKey: "created_by",
    as: "creator",
  });

  // RecipeMaster belongs to User (updated_by)
  RecipeMaster.belongsTo(models.User, {
    foreignKey: "updated_by",
    as: "updater",
  });

  // Cross-version associations through junction tables
  // These allow querying data across all versions of a recipe
  RecipeMaster.hasMany(models.RecipeIngredients, {
    foreignKey: "recipe_id",
    sourceKey: "current_published_version_id",
    as: "currentIngredients",
    constraints: false,
  });

  RecipeMaster.hasMany(models.RecipeSteps, {
    foreignKey: "recipe_id",
    sourceKey: "current_published_version_id",
    as: "currentSteps",
    constraints: false,
  });

  RecipeMaster.hasMany(models.RecipeCategory, {
    foreignKey: "recipe_id",
    sourceKey: "current_published_version_id",
    as: "currentCategories",
    constraints: false,
  });

  RecipeMaster.hasMany(models.RecipeAttributes, {
    foreignKey: "recipe_id",
    sourceKey: "current_published_version_id",
    as: "currentAttributes",
    constraints: false,
  });

  RecipeMaster.hasMany(models.RecipeResources, {
    foreignKey: "recipe_id",
    sourceKey: "current_published_version_id",
    as: "currentResources",
    constraints: false,
  });

  // History across all versions
  RecipeMaster.hasMany(models.RecipeHistory, {
    foreignKey: "recipe_id",
    through: models.Recipe,
    as: "allVersionsHistory",
    constraints: false,
  });
};

export default RecipeMaster;
