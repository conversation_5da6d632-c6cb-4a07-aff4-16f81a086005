{"version": 3, "sources": ["../../src/errors/database-error.ts"], "sourcesContent": ["import BaseError, { CommonErrorProperties, ErrorOptions } from './base-error';\n\nexport interface DatabaseErrorParent\n  extends <PERSON>rro<PERSON>,\n    Pick<CommonErrorProperties, 'sql'> {\n  /** The parameters for the sql that triggered the error */\n  readonly parameters?: object;\n}\n\nexport interface DatabaseErrorSubclassOptions extends ErrorOptions {\n  parent?: DatabaseErrorParent;\n  message?: string;\n}\n\n/**\n * A base class for all database related errors.\n */\nclass DatabaseError\n  extends BaseError\n  implements DatabaseErrorParent, CommonErrorProperties\n{\n  parent: Error;\n  original: Error;\n  sql: string;\n  parameters: object;\n\n  /**\n   * @param parent The database specific error which triggered this one\n   * @param options\n   */\n  constructor(parent: DatabaseErrorParent, options: ErrorOptions = {}) {\n    super(parent.message);\n    this.name = 'SequelizeDatabaseError';\n\n    this.parent = parent;\n    this.original = parent;\n\n    this.sql = parent.sql;\n    this.parameters = parent.parameters ?? {};\n\n    if (options.stack) {\n      this.stack = options.stack;\n    }\n  }\n}\n\nexport default DatabaseError;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA,wBAA+D;AAiB/D,4BACU,0BAEV;AAAA,EAUE,YAAY,QAA6B,UAAwB,IAAI;AACnE,UAAM,OAAO;AAVf;AACA;AACA;AACA;AAxBF;AAgCI,SAAK,OAAO;AAEZ,SAAK,SAAS;AACd,SAAK,WAAW;AAEhB,SAAK,MAAM,OAAO;AAClB,SAAK,aAAa,aAAO,eAAP,YAAqB;AAEvC,QAAI,QAAQ,OAAO;AACjB,WAAK,QAAQ,QAAQ;AAAA;AAAA;AAAA;AAK3B,IAAO,yBAAQ;", "names": []}