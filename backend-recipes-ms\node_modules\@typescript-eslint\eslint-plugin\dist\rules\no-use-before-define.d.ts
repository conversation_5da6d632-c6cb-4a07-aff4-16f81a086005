import { TSESLint } from '@typescript-eslint/utils';
export interface Config {
    allowNamedExports?: boolean;
    classes?: boolean;
    enums?: boolean;
    functions?: boolean;
    ignoreTypeReferences?: boolean;
    typedefs?: boolean;
    variables?: boolean;
}
export type Options = ['nofunc' | Config];
export type MessageIds = 'noUseBeforeDefine';
declare const _default: TSESLint.RuleModule<"noUseBeforeDefine", Options, import("../../rules").ESLintPluginDocs, TSESLint.RuleListener>;
export default _default;
//# sourceMappingURL=no-use-before-define.d.ts.map