{"version": 3, "sources": ["../../../src/dialects/abstract/index.js"], "sourcesContent": ["'use strict';\n\nclass AbstractDialect {\n  /**\n   * Whether this dialect can use \\ in strings to escape string delimiters.\n   *\n   * @returns {boolean}\n   */\n  canBackslashEscape() {\n    return false;\n  }\n}\n\nAbstractDialect.prototype.supports = {\n  'DEFAULT': true,\n  'DEFAULT VALUES': false,\n  'VALUES ()': false,\n  'LIMIT ON UPDATE': false,\n  'ON DUPLICATE KEY': true,\n  'ORDER NULLS': false,\n  'UNION': true,\n  'UNION ALL': true,\n  'RIGHT JOIN': true,\n\n  /* does the dialect support returning values for inserted/updated fields */\n  returnValues: false,\n\n  /* features specific to autoIncrement values */\n  autoIncrement: {\n    /* does the dialect require modification of insert queries when inserting auto increment fields */\n    identityInsert: false,\n\n    /* does the dialect support inserting default/null values for autoincrement fields */\n    defaultValue: true,\n\n    /* does the dialect support updating autoincrement fields */\n    update: true\n  },\n  /* Do we need to say DEFAULT for bulk insert */\n  bulkDefault: false,\n  schemas: false,\n  transactions: true,\n  settingIsolationLevelDuringTransaction: true,\n  transactionOptions: {\n    type: false\n  },\n  migrations: true,\n  upserts: true,\n  inserts: {\n    ignoreDuplicates: '', /* dialect specific words for INSERT IGNORE or DO NOTHING */\n    updateOnDuplicate: false, /* whether dialect supports ON DUPLICATE KEY UPDATE */\n    onConflictDoNothing: '', /* dialect specific words for ON CONFLICT DO NOTHING */\n    onConflictWhere: false, /* whether dialect supports ON CONFLICT WHERE */\n    conflictFields: false /* whether the dialect supports specifying conflict fields or not */\n  },\n  constraints: {\n    restrict: true,\n    addConstraint: true,\n    dropConstraint: true,\n    unique: true,\n    default: false,\n    check: true,\n    foreignKey: true,\n    primaryKey: true\n  },\n  index: {\n    collate: true,\n    length: false,\n    parser: false,\n    concurrently: false,\n    type: false,\n    using: true,\n    functionBased: false,\n    operator: false\n  },\n  groupedLimit: true,\n  indexViaAlter: false,\n  JSON: false,\n  /**\n   * This dialect supports marking a column's constraints as deferrable.\n   * e.g. 'DEFERRABLE' and 'INITIALLY DEFERRED'\n   */\n  deferrableConstraints: false,\n  escapeStringConstants: false\n};\n\nmodule.exports = AbstractDialect;\nmodule.exports.AbstractDialect = AbstractDialect;\nmodule.exports.default = AbstractDialect;\n"], "mappings": ";AAEA,sBAAsB;AAAA,EAMpB,qBAAqB;AACnB,WAAO;AAAA;AAAA;AAIX,gBAAgB,UAAU,WAAW;AAAA,EACnC,WAAW;AAAA,EACX,kBAAkB;AAAA,EAClB,aAAa;AAAA,EACb,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,eAAe;AAAA,EACf,SAAS;AAAA,EACT,aAAa;AAAA,EACb,cAAc;AAAA,EAGd,cAAc;AAAA,EAGd,eAAe;AAAA,IAEb,gBAAgB;AAAA,IAGhB,cAAc;AAAA,IAGd,QAAQ;AAAA;AAAA,EAGV,aAAa;AAAA,EACb,SAAS;AAAA,EACT,cAAc;AAAA,EACd,wCAAwC;AAAA,EACxC,oBAAoB;AAAA,IAClB,MAAM;AAAA;AAAA,EAER,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,SAAS;AAAA,IACP,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,qBAAqB;AAAA,IACrB,iBAAiB;AAAA,IACjB,gBAAgB;AAAA;AAAA,EAElB,aAAa;AAAA,IACX,UAAU;AAAA,IACV,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,YAAY;AAAA;AAAA,EAEd,OAAO;AAAA,IACL,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,MAAM;AAAA,IACN,OAAO;AAAA,IACP,eAAe;AAAA,IACf,UAAU;AAAA;AAAA,EAEZ,cAAc;AAAA,EACd,eAAe;AAAA,EACf,MAAM;AAAA,EAKN,uBAAuB;AAAA,EACvB,uBAAuB;AAAA;AAGzB,OAAO,UAAU;AACjB,OAAO,QAAQ,kBAAkB;AACjC,OAAO,QAAQ,UAAU;", "names": []}