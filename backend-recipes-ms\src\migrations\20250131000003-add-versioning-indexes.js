'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    console.log('🚀 Adding versioning indexes...');
    
    // Indexes for mo_recipe_master table
    await queryInterface.addIndex('mo_recipe_master', {
      fields: ['master_recipe_slug'],
      unique: true,
      name: 'unique_master_recipe_slug',
    });

    await queryInterface.addIndex('mo_recipe_master', {
      fields: ['organization_id'],
      name: 'idx_master_recipe_organization',
    });

    await queryInterface.addIndex('mo_recipe_master', {
      fields: ['is_active'],
      name: 'idx_master_recipe_active',
    });

    await queryInterface.addIndex('mo_recipe_master', {
      fields: ['current_published_version_id'],
      name: 'idx_master_recipe_published_version',
    });

    await queryInterface.addIndex('mo_recipe_master', {
      fields: ['current_draft_version_id'],
      name: 'idx_master_recipe_draft_version',
    });

    // Indexes for mo_recipe table versioning columns
    await queryInterface.addIndex('mo_recipe', {
      fields: ['master_recipe_id'],
      name: 'idx_recipe_master_recipe_id',
    });

    await queryInterface.addIndex('mo_recipe', {
      fields: ['master_recipe_id', 'version_status', 'is_current_version'],
      name: 'idx_recipe_master_version_status',
    });

    await queryInterface.addIndex('mo_recipe', {
      fields: ['version_status', 'is_current_version'],
      name: 'idx_recipe_version_status_current',
    });

    await queryInterface.addIndex('mo_recipe', {
      fields: ['parent_version_id'],
      name: 'idx_recipe_parent_version_id',
    });

    await queryInterface.addIndex('mo_recipe', {
      fields: ['version_number'],
      name: 'idx_recipe_version_number',
    });

    await queryInterface.addIndex('mo_recipe', {
      fields: ['version_created_at'],
      name: 'idx_recipe_version_created_at',
    });

    await queryInterface.addIndex('mo_recipe', {
      fields: ['version_published_at'],
      name: 'idx_recipe_version_published_at',
    });

    // Composite index for efficient version queries
    await queryInterface.addIndex('mo_recipe', {
      fields: ['master_recipe_id', 'version_number'],
      name: 'idx_recipe_master_version_number',
    });

    // Index for organization-specific version queries
    await queryInterface.addIndex('mo_recipe', {
      fields: ['organization_id', 'version_status', 'is_current_version'],
      name: 'idx_recipe_org_version_status',
    });

    console.log('✅ Versioning indexes added successfully');
  },

  async down(queryInterface, Sequelize) {
    console.log('🗑️  Removing versioning indexes...');
    
    // Remove mo_recipe versioning indexes
    await queryInterface.removeIndex('mo_recipe', 'idx_recipe_org_version_status');
    await queryInterface.removeIndex('mo_recipe', 'idx_recipe_master_version_number');
    await queryInterface.removeIndex('mo_recipe', 'idx_recipe_version_published_at');
    await queryInterface.removeIndex('mo_recipe', 'idx_recipe_version_created_at');
    await queryInterface.removeIndex('mo_recipe', 'idx_recipe_version_number');
    await queryInterface.removeIndex('mo_recipe', 'idx_recipe_parent_version_id');
    await queryInterface.removeIndex('mo_recipe', 'idx_recipe_version_status_current');
    await queryInterface.removeIndex('mo_recipe', 'idx_recipe_master_version_status');
    await queryInterface.removeIndex('mo_recipe', 'idx_recipe_master_recipe_id');

    // Remove mo_recipe_master indexes
    await queryInterface.removeIndex('mo_recipe_master', 'idx_master_recipe_draft_version');
    await queryInterface.removeIndex('mo_recipe_master', 'idx_master_recipe_published_version');
    await queryInterface.removeIndex('mo_recipe_master', 'idx_master_recipe_active');
    await queryInterface.removeIndex('mo_recipe_master', 'idx_master_recipe_organization');
    await queryInterface.removeIndex('mo_recipe_master', 'unique_master_recipe_slug');

    console.log('✅ Versioning indexes removed successfully');
  }
};
