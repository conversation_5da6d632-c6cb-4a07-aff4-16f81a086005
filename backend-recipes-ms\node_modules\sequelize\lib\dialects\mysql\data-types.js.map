{"version": 3, "sources": ["../../../src/dialects/mysql/data-types.js"], "sourcesContent": ["'use strict';\n\nconst wkx = require('wkx');\nconst _ = require('lodash');\nconst momentTz = require('moment-timezone');\nconst moment = require('moment');\n\nmodule.exports = BaseTypes => {\n  BaseTypes.ABSTRACT.prototype.dialectTypes = 'https://dev.mysql.com/doc/refman/5.7/en/data-types.html';\n\n  /**\n   * types: [buffer_type, ...]\n   *\n   * @see buffer_type here https://dev.mysql.com/doc/refman/5.7/en/c-api-prepared-statement-type-codes.html\n   * @see hex here https://github.com/sidorares/node-mysql2/blob/master/lib/constants/types.js\n   */\n\n  BaseTypes.DATE.types.mysql = ['DATETIME'];\n  BaseTypes.STRING.types.mysql = ['VAR_STRING'];\n  BaseTypes.CHAR.types.mysql = ['STRING'];\n  BaseTypes.TEXT.types.mysql = ['BLOB'];\n  BaseTypes.TINYINT.types.mysql = ['TINY'];\n  BaseTypes.SMALLINT.types.mysql = ['SHORT'];\n  BaseTypes.MEDIUMINT.types.mysql = ['INT24'];\n  BaseTypes.INTEGER.types.mysql = ['LONG'];\n  BaseTypes.BIGINT.types.mysql = ['LONGLONG'];\n  BaseTypes.FLOAT.types.mysql = ['FLOAT'];\n  BaseTypes.TIME.types.mysql = ['TIME'];\n  BaseTypes.DATEONLY.types.mysql = ['DATE'];\n  BaseTypes.BOOLEAN.types.mysql = ['TINY'];\n  BaseTypes.BLOB.types.mysql = ['TINYBLOB', 'BLOB', 'LONGBLOB'];\n  BaseTypes.DECIMAL.types.mysql = ['NEWDECIMAL'];\n  BaseTypes.UUID.types.mysql = false;\n  BaseTypes.ENUM.types.mysql = false;\n  BaseTypes.REAL.types.mysql = ['DOUBLE'];\n  BaseTypes.DOUBLE.types.mysql = ['DOUBLE'];\n  BaseTypes.GEOMETRY.types.mysql = ['GEOMETRY'];\n  BaseTypes.JSON.types.mysql = ['JSON'];\n\n  class DECIMAL extends BaseTypes.DECIMAL {\n    toSql() {\n      let definition = super.toSql();\n      if (this._unsigned) {\n        definition += ' UNSIGNED';\n      }\n      if (this._zerofill) {\n        definition += ' ZEROFILL';\n      }\n      return definition;\n    }\n  }\n\n  class DATE extends BaseTypes.DATE {\n    toSql() {\n      return this._length ? `DATETIME(${this._length})` : 'DATETIME';\n    }\n    _stringify(date, options) {\n      if (!moment.isMoment(date)) {\n        date = this._applyTimezone(date, options);\n      }\n      // Fractional DATETIMEs only supported on MySQL 5.6.4+\n      if (this._length) {\n        return date.format('YYYY-MM-DD HH:mm:ss.SSS');\n      }\n      return date.format('YYYY-MM-DD HH:mm:ss');\n    }\n    static parse(value, options) {\n      value = value.string();\n      if (value === null) {\n        return value;\n      }\n      if (momentTz.tz.zone(options.timezone)) {\n        value = momentTz.tz(value, options.timezone).toDate();\n      }\n      else {\n        value = new Date(`${value} ${options.timezone}`);\n      }\n      return value;\n    }\n  }\n\n  class DATEONLY extends BaseTypes.DATEONLY {\n    static parse(value) {\n      return value.string();\n    }\n  }\n  class UUID extends BaseTypes.UUID {\n    toSql() {\n      return 'CHAR(36) BINARY';\n    }\n  }\n\n  const SUPPORTED_GEOMETRY_TYPES = ['POINT', 'LINESTRING', 'POLYGON'];\n\n  class GEOMETRY extends BaseTypes.GEOMETRY {\n    constructor(type, srid) {\n      super(type, srid);\n      if (_.isEmpty(this.type)) {\n        this.sqlType = this.key;\n        return;\n      }\n      if (SUPPORTED_GEOMETRY_TYPES.includes(this.type)) {\n        this.sqlType = this.type;\n        return;\n      }\n      throw new Error(`Supported geometry types are: ${SUPPORTED_GEOMETRY_TYPES.join(', ')}`);\n    }\n    static parse(value) {\n      value = value.buffer();\n      // Empty buffer, MySQL doesn't support POINT EMPTY\n      // check, https://dev.mysql.com/worklog/task/?id=2381\n      if (!value || value.length === 0) {\n        return null;\n      }\n      // For some reason, discard the first 4 bytes\n      value = value.slice(4);\n      return wkx.Geometry.parse(value).toGeoJSON({ shortCrs: true });\n    }\n    toSql() {\n      return this.sqlType;\n    }\n  }\n\n  class ENUM extends BaseTypes.ENUM {\n    toSql(options) {\n      return `ENUM(${this.values.map(value => options.escape(value)).join(', ')})`;\n    }\n  }\n\n  class JSONTYPE extends BaseTypes.JSON {\n    _stringify(value, options) {\n      return options.operation === 'where' && typeof value === 'string' ? value : JSON.stringify(value);\n    }\n  }\n\n  return {\n    ENUM,\n    DATE,\n    DATEONLY,\n    UUID,\n    GEOMETRY,\n    DECIMAL,\n    JSON: JSONTYPE\n  };\n};\n"], "mappings": ";AAEA,MAAM,MAAM,QAAQ;AACpB,MAAM,IAAI,QAAQ;AAClB,MAAM,WAAW,QAAQ;AACzB,MAAM,SAAS,QAAQ;AAEvB,OAAO,UAAU,eAAa;AAC5B,YAAU,SAAS,UAAU,eAAe;AAS5C,YAAU,KAAK,MAAM,QAAQ,CAAC;AAC9B,YAAU,OAAO,MAAM,QAAQ,CAAC;AAChC,YAAU,KAAK,MAAM,QAAQ,CAAC;AAC9B,YAAU,KAAK,MAAM,QAAQ,CAAC;AAC9B,YAAU,QAAQ,MAAM,QAAQ,CAAC;AACjC,YAAU,SAAS,MAAM,QAAQ,CAAC;AAClC,YAAU,UAAU,MAAM,QAAQ,CAAC;AACnC,YAAU,QAAQ,MAAM,QAAQ,CAAC;AACjC,YAAU,OAAO,MAAM,QAAQ,CAAC;AAChC,YAAU,MAAM,MAAM,QAAQ,CAAC;AAC/B,YAAU,KAAK,MAAM,QAAQ,CAAC;AAC9B,YAAU,SAAS,MAAM,QAAQ,CAAC;AAClC,YAAU,QAAQ,MAAM,QAAQ,CAAC;AACjC,YAAU,KAAK,MAAM,QAAQ,CAAC,YAAY,QAAQ;AAClD,YAAU,QAAQ,MAAM,QAAQ,CAAC;AACjC,YAAU,KAAK,MAAM,QAAQ;AAC7B,YAAU,KAAK,MAAM,QAAQ;AAC7B,YAAU,KAAK,MAAM,QAAQ,CAAC;AAC9B,YAAU,OAAO,MAAM,QAAQ,CAAC;AAChC,YAAU,SAAS,MAAM,QAAQ,CAAC;AAClC,YAAU,KAAK,MAAM,QAAQ,CAAC;AAE9B,wBAAsB,UAAU,QAAQ;AAAA,IACtC,QAAQ;AACN,UAAI,aAAa,MAAM;AACvB,UAAI,KAAK,WAAW;AAClB,sBAAc;AAAA;AAEhB,UAAI,KAAK,WAAW;AAClB,sBAAc;AAAA;AAEhB,aAAO;AAAA;AAAA;AAIX,qBAAmB,UAAU,KAAK;AAAA,IAChC,QAAQ;AACN,aAAO,KAAK,UAAU,YAAY,KAAK,aAAa;AAAA;AAAA,IAEtD,WAAW,MAAM,SAAS;AACxB,UAAI,CAAC,OAAO,SAAS,OAAO;AAC1B,eAAO,KAAK,eAAe,MAAM;AAAA;AAGnC,UAAI,KAAK,SAAS;AAChB,eAAO,KAAK,OAAO;AAAA;AAErB,aAAO,KAAK,OAAO;AAAA;AAAA,WAEd,MAAM,OAAO,SAAS;AAC3B,cAAQ,MAAM;AACd,UAAI,UAAU,MAAM;AAClB,eAAO;AAAA;AAET,UAAI,SAAS,GAAG,KAAK,QAAQ,WAAW;AACtC,gBAAQ,SAAS,GAAG,OAAO,QAAQ,UAAU;AAAA,aAE1C;AACH,gBAAQ,IAAI,KAAK,GAAG,SAAS,QAAQ;AAAA;AAEvC,aAAO;AAAA;AAAA;AAIX,yBAAuB,UAAU,SAAS;AAAA,WACjC,MAAM,OAAO;AAClB,aAAO,MAAM;AAAA;AAAA;AAGjB,qBAAmB,UAAU,KAAK;AAAA,IAChC,QAAQ;AACN,aAAO;AAAA;AAAA;AAIX,QAAM,2BAA2B,CAAC,SAAS,cAAc;AAEzD,yBAAuB,UAAU,SAAS;AAAA,IACxC,YAAY,MAAM,MAAM;AACtB,YAAM,MAAM;AACZ,UAAI,EAAE,QAAQ,KAAK,OAAO;AACxB,aAAK,UAAU,KAAK;AACpB;AAAA;AAEF,UAAI,yBAAyB,SAAS,KAAK,OAAO;AAChD,aAAK,UAAU,KAAK;AACpB;AAAA;AAEF,YAAM,IAAI,MAAM,iCAAiC,yBAAyB,KAAK;AAAA;AAAA,WAE1E,MAAM,OAAO;AAClB,cAAQ,MAAM;AAGd,UAAI,CAAC,SAAS,MAAM,WAAW,GAAG;AAChC,eAAO;AAAA;AAGT,cAAQ,MAAM,MAAM;AACpB,aAAO,IAAI,SAAS,MAAM,OAAO,UAAU,EAAE,UAAU;AAAA;AAAA,IAEzD,QAAQ;AACN,aAAO,KAAK;AAAA;AAAA;AAIhB,qBAAmB,UAAU,KAAK;AAAA,IAChC,MAAM,SAAS;AACb,aAAO,QAAQ,KAAK,OAAO,IAAI,WAAS,QAAQ,OAAO,QAAQ,KAAK;AAAA;AAAA;AAIxE,yBAAuB,UAAU,KAAK;AAAA,IACpC,WAAW,OAAO,SAAS;AACzB,aAAO,QAAQ,cAAc,WAAW,OAAO,UAAU,WAAW,QAAQ,KAAK,UAAU;AAAA;AAAA;AAI/F,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA;AAAA;", "names": []}