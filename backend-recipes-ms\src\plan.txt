RECIPE VERSIONING SYSTEM - DETAILED IMPLEMENTATION PLAN

PROJECT OVERVIEW:

- Implement comprehensive recipe versioning system
- Support draft/publish/revised status workflow
- Maintain maximum 3 versions per recipe
- Role-based access (<PERSON><PERSON> sees drafts, Users see published)
- Update history tracking for version changes
- Modify highlight system for version-aware changes

TOTAL ESTIMATED TIME: 2-3 working days (16-27 hours)
TEAM SIZE: 1 developer
WORKING HOURS: 8 hours per day
PRIORITY: High (Core feature enhancement)

# =====================================================
DAY 1: DATABASE SCHEMA & CORE HELPERS (8 hours)

1.1 CREATE MASTER RECIPE TABLE & SCHEMA CHANGES
Files:
- migrations/001-create-recipe-master-table.sql
- migrations/002-add-versioning-columns.sql
Description: Create master table + add versioning columns to existing tables
ETA: 2 hours

1.2 CREATE INDEXES & MIGRATION SCRIPT
Files:
- migrations/003-add-versioning-indexes.sql
- scripts/migrate-existing-recipes.ts
Description: Performance indexes + data migration script
ETA: 2 hours

1.3 CORE VERSIONING HELPER FUNCTIONS
File: src/helper/recipe-versioning.helper.ts
Functions:
- createNewRecipeVersion()
- updateVersionStatuses()
- getActiveRecipeVersion()
- cleanupOldVersions()
ETA: 2.5 hours

1.4 UPDATE EXISTING RECIPE HELPERS
File: src/helper/recipe.helper.ts (modifications)
Functions:
- getRecipeByIdWithVersioning()
- getVersionedRelatedData()
- getRecipesListRaw() (version-aware)
- getRecipeInListFormat() (version-aware)
- addFreshnessIndicators() (version-specific)
ETA: 1.5 hours

1.5 UPDATE BATCH HELPER FUNCTIONS
File: src/helper/recipe-batch-common.helper.ts (modifications)
Functions:
- validateRecipeAccess() (version-aware)
- createConsolidatedHistoryEntry() (version context)
- updateRecipeTimestamp() (version-specific)
ETA: 1 hour

1.6 UPDATE HISTORY TRACKING FOR VERSIONING
File: src/helper/recipe.helper.ts (createRecipeHistory modifications)
Changes: Add version-specific history tracking
New Actions: version_created, version_published, version_archived
ETA: 1 hour

# =====================================================
DAY 2: CRITICAL API MODIFICATIONS (10 hours)

2.1 RECIPE BATCH CREATION APIs
Endpoints:
- POST /api/v1/private/recipe/basic-info
- POST /api/v1/private/recipe/ingredients-nutrition
- POST /api/v1/private/recipe/recipe-steps
- POST /api/v1/private/recipe/add-recipe-resources
File: src/controller/recipe-batch.controller.ts
Functions: createRecipeBasicInfo, addIngredientsNutritionCuisine, addRecipeSteps, addRecipeUploads
Changes: Create master record + v1 entries for batch operations
History: Track version creation and batch updates
Dependencies: Updated helper functions from Day 1
ETA: 4.5 hours

2.2 RECIPE GET BY ID API
Endpoint: GET /api/v1/private/recipe/get-by-id/:id
File: src/controller/recipe.controller.ts (getRecipeById function)
Changes: Role-based version retrieval + version-aware highlights
Dependencies: getRecipeByIdRaw, addFreshnessIndicators
ETA: 2 hours

2.3 RECIPE LIST API
Endpoint: GET /api/v1/private/recipe/list
File: src/controller/recipe.controller.ts (getRecipesList function)
Changes: Version-aware filtering, admin vs user view + bulk highlights
Dependencies: getRecipesListRaw, getRecipeInListFormat
ETA: 2 hours

2.4 RECIPE DUPLICATE API
Endpoint: POST /api/v1/private/recipe/duplicate/:id
File: src/controller/recipe.controller.ts (duplicateRecipe function)
Changes: Duplicate from correct version based on user role
Dependencies: Version-aware recipe retrieval
ETA: 1.5 hours

# =====================================================
DAY 3: ADDITIONAL APIs, HIGHLIGHTS & TESTING (8 hours)

3.1 RECIPE BOOKMARK API
Endpoint: POST/DELETE /api/v1/private/recipe/bookmark/:id
File: src/controller/recipe.controller.ts (bookmarkRecipe functions)
Changes: Version-aware bookmarking (bookmark master recipe)
ETA: 1 hour

3.2 PUBLIC RECIPE APIs
Endpoints:
- GET /api/v1/public/recipe/get-by-id/:id
- GET /api/v1/public/recipe/list
File: src/controller/recipe.controller.ts
Changes: Only show published versions
Dependencies: Version-filtered helper functions
ETA: 1.5 hours

3.3 RECIPE SEARCH API
Endpoint: GET /api/v1/private/recipe/search
File: src/controller/recipe.controller.ts (searchRecipes function)
Changes: Version-aware search results
ETA: 1 hour

3.4 UPDATE HIGHLIGHT SYSTEM FOR VERSIONING
File: src/helper/recipe-highlight.helper.ts
Functions:
- getRecipeHighlight() - version-aware highlights
- getBulkRecipeHighlights() - version-aware bulk highlights
- getSimpleRecipeHighlight() - version-aware simple highlights
- hasRecentChanges() - version-aware recent changes
Changes: Show highlights based on active version for user role
ETA: 2.5 hours

3.5 ENHANCED HISTORY TRACKING SYSTEM
File: src/helper/recipe.helper.ts
Functions:
- formatHistoryAsCategorized() - version-aware history
- getRecipeCategorizedHistory() - version context
Changes: Enhanced history with version information
ETA: 1 hour

3.6 BASIC TESTING & VALIDATION
Files:
- Basic unit tests for versioning functions
- Integration testing of modified APIs (batch + core)
- Migration testing with sample data
- History tracking validation
- Highlight system validation
ETA: 1 hour

# =====================================================
DETAILED API CHANGES BREAKDOWN

CRITICAL BATCH APIs (Day 2 - Must be completed):

1. POST /api/v1/private/recipe/basic-info
    - Impact: High
    - Complexity: High (version creation logic)
    - History: Track version creation
    - Dependencies: validateRecipeAccess, createConsolidatedHistoryEntry
    - ETA: 1.5 hours
2. POST /api/v1/private/recipe/ingredients-nutrition
    - Impact: High
    - Complexity: High (version-aware ingredient handling)
    - History: Track ingredient version updates
    - Dependencies: captureOldRecipeData, version-aware validation
    - ETA: 1.5 hours
3. POST /api/v1/private/recipe/recipe-steps
    - Impact: High
    - Complexity: Medium (version-aware step handling)
    - History: Track step version updates
    - Dependencies: createConsolidatedHistoryEntry
    - ETA: 1 hour
4. POST /api/v1/private/recipe/add-recipe-resources
    - Impact: High
    - Complexity: Medium (version-aware resource handling)
    - History: Track resource version updates
    - Dependencies: validateRecipeAccess, captureOldRecipeData
    - ETA: 0.5 hours

CRITICAL CORE APIs (Day 2 - Must be completed):
5. GET /api/v1/private/recipe/get-by-id/:id

- Impact: High
- Complexity: High (role-based version retrieval)
- Highlights: Version-aware highlight display
- Dependencies: getRecipeByIdRaw, addFreshnessIndicators, getRecipeHighlight
- ETA: 2 hours
1. GET /api/v1/private/recipe/list
    - Impact: High
    - Complexity: High (version-aware filtering + bulk highlights)
    - Highlights: Version-aware bulk highlights
    - Dependencies: getRecipesListRaw, getRecipeInListFormat, getBulkRecipeHighlights
    - ETA: 2 hours
2. POST /api/v1/private/recipe/duplicate/:id
    - Impact: Medium
    - Complexity: Medium (version-aware duplication)
    - Dependencies: Version-aware recipe retrieval, createRecipeHistory
    - ETA: 1.5 hours

MODERATE IMPACT APIs (Day 3):
8. POST/DELETE /api/v1/private/recipe/bookmark/:id

- Impact: Medium
- Complexity: Low (bookmark master recipe)
- Changes: Version-aware bookmarking
- ETA: 1 hour
1. GET /api/v1/public/recipe/get-by-id/:id
    - Impact: Medium
    - Complexity: Low
    - Changes: Published version only
    - ETA: 0.75 hours
2. GET /api/v1/public/recipe/list
    - Impact: Medium
    - Complexity: Low
    - Changes: Published versions only
    - ETA: 0.75 hours
3. GET /api/v1/private/recipe/search
    - Impact: Medium
    - Complexity: Medium
    - Changes: Version-aware search
    - ETA: 1 hour

DEFERRED APIs (Post-implementation):
12. PUT /api/v1/private/recipe/update/:id (Legacy - handled by batch APIs)
13. POST /api/v1/private/recipe/create (Legacy - handled by batch APIs)
14. GET /api/v1/private/recipe/export/:id
15. Recipe Analytics APIs

# =====================================================
HELPER FUNCTION MODIFICATIONS

BATCH CONTROLLER HELPERS:
File: src/helper/recipe-batch-common.helper.ts

1. validateRecipeAccess() - Add version-aware validation
2. createConsolidatedHistoryEntry() - Add version context to history
3. updateRecipeTimestamp() - Update version-specific timestamps
4. captureOldRecipeData() - Capture version-specific data
ETA: 1 hour

RECIPE CONTROLLER HELPERS:
File: src/helper/recipe.helper.ts

1. getRecipeByIdRaw() - Add version-aware retrieval
2. getRecipesListRaw() - Add version filtering logic
3. getRecipeInListFormat() - Add version-aware formatting
4. addFreshnessIndicators() - Add version-specific freshness checks
5. createRecipeHistory() - Enhanced version context
6. formatHistoryAsCategorized() - Version-aware history formatting
ETA: 1.5 hours

HIGHLIGHT HELPERS:
File: src/helper/recipe-highlight.helper.ts

1. getRecipeHighlight() - Version-aware single recipe highlights
2. getBulkRecipeHighlights() - Version-aware bulk highlights
3. getSimpleRecipeHighlight() - Version-aware simple highlights
4. hasRecentChanges() - Version-aware recent changes detection
ETA: 2.5 hours

FRESHNESS HELPERS:
File: src/helper/cost-freshness.helper.ts

1. addFreshnessIndicatorsWithAutoUpdate() - Version-specific freshness
2. checkAndUpdateRecipeCosts() - Version-aware cost updates
ETA: 0.5 hours

# =====================================================
BATCH API VERSIONING STRATEGY

BATCH CREATION WORKFLOW:

1. createRecipeBasicInfo - Creates master + v1 with basic info
2. addIngredientsNutritionCuisine - Updates v1 with ingredients/nutrition
3. addRecipeSteps - Updates v1 with steps
4. addRecipeUploads - Updates v1 with resources

BATCH UPDATE WORKFLOW:

1. Check if recipe has published version
2. If published, create new version (v2/v3)
3. If draft, update existing draft version
4. Apply same batch API logic to new/existing version

VERSION STATUS MANAGEMENT:

- Draft: Can be updated via batch APIs
- Published: Creates new version on update
- Revised: Replaces published version

HISTORY & HIGHLIGHTS:

- Track all version changes in history
- Show highlights based on user role and version access

# =====================================================
SIMPLIFIED IMPLEMENTATION APPROACH

PHASE 1 FOCUS:

- Core versioning logic for batch APIs only
- Enhanced history tracking for version changes
- Version-aware highlight system
- Minimal testing (core functionality only)
- No legacy API modifications needed

PHASE 2 ENHANCEMENTS (Future sprints):

- Advanced file versioning
- Comprehensive history tracking with version comparisons
- Enhanced version-specific analytics
- Performance optimizations
- Comprehensive test coverage

# =====================================================
RISK MITIGATION (SIMPLIFIED)

HIGH RISK:

1. Data Loss During Migration
Mitigation: Database backup before migration
Time Buffer: Included in Day 1 estimates
2. API Breaking Changes (Batch APIs)
Mitigation: Maintain backward compatibility for batch workflow
Time Buffer: Built into API modification estimates
3. History Data Inconsistency
Mitigation: Careful version context tracking
Time Buffer: Included in testing phase

MEDIUM RISK:

1. Complex Version Logic Bugs in Batch Operations
Mitigation: Focus on simple, tested logic patterns
Time Buffer: Included in Day 3 testing
2. Highlight System Version Conflicts
Mitigation: Clear role-based version access rules
Time Buffer: Built into highlight modification estimates

# =====================================================
DEPLOYMENT STRATEGY (SIMPLIFIED)

STAGE 1: Database Migration (Day 3 - 1 hour)

- Run migration scripts on staging
- Verify data integrity
- Test history tracking
- Quick rollback test

STAGE 2: API Deployment (Day 3 - 0.5 hours)

- Deploy to staging environment
- Test batch API workflows
- Validate history and highlight functionality
- Deploy to production

STAGE 3: Monitoring (Ongoing)

- Monitor API performance (batch APIs)
- Validate data consistency
- Monitor history tracking accuracy
- User feedback collection

# =====================================================
RESOURCE REQUIREMENTS (MINIMAL)

DEVELOPMENT TEAM:

- 1 Senior Backend Developer: Full time (3 days)

INFRASTRUCTURE:

- Existing staging environment
- Database backup capability
- Basic monitoring tools

EXTERNAL DEPENDENCIES:

- None (internal feature enhancement)

# =====================================================
SUCCESS METRICS (SIMPLIFIED)

TECHNICAL METRICS:

- Zero data loss during migration
- All critical batch APIs functional
- Basic version workflow working for batch APIs
- History tracking accurate for version changes
- Highlights display correctly based on user role
- No breaking changes for existing batch workflow

BUSINESS METRICS:

- Recipe draft/publish workflow operational
- Admin/user role-based access working
- Basic version management functional
- Batch recipe creation workflow maintained
- Version change tracking operational

# =====================================================
POST-IMPLEMENTATION TASKS (DEFERRED)

IMMEDIATE (Week 1):

1. Basic documentation updates (0.5 days)
2. User training materials (0.5 days)

FUTURE SPRINTS:

1. Legacy API versioning support
2. Advanced file versioning
3. Comprehensive history tracking with version comparisons
4. Performance optimizations
5. Enhanced version-specific analytics
6. Complete test coverage
7. Advanced highlight features

TOTAL PROJECT TIMELINE: 3 working days (27 hours)
BUFFER: 3 hours built into estimates
FINAL ESTIMATE: 3 working days with extended hours

# =====================================================
DAILY BREAKDOWN SUMMARY (UPDATED)

DAY 1 (9 hours): Database + Core Logic + Helper Updates

- Database schema changes (4 hours)
- Core versioning helpers (2.5 hours)
- Batch helper function updates (1 hour)
- Recipe helper function updates (1.5 hours)

DAY 2 (10 hours): Critical APIs (Batch + Core)

- Recipe batch APIs with versioning + history (4.5 hours)
- Recipe get/list APIs with highlights (4 hours)
- Recipe duplicate API (1.5 hours)

DAY 3 (8 hours): Additional APIs, Enhanced Features + Deployment

- Recipe bookmark API (1 hour)
- Public APIs (1.5 hours)
- Recipe search API (1 hour)
- Highlight system updates (2.5 hours)
- Enhanced history tracking (1 hour)
- Testing & deployment (1 hour)

# =====================================================
APPROVAL REQUIRED FOR:

- Database schema changes (Day 1)
- Production deployment window (Day 3)
- Resource allocation (1 developer for 3 days)
- Batch API workflow modifications
- History tracking schema updates
- Highlight system modifications