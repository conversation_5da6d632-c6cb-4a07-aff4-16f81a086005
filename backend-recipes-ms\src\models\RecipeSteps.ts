import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

export enum RecipeStepsStatus {
  active = "active",
  inactive = "inactive",
}

interface RecipeStepsAttributes {
  id?: number;
  recipe_id: number;
  item_id?: number;
  recipe_step_order: number;
  recipe_step_description?: string;
  status: RecipeStepsStatus;
  organization_id?: string;
  created_by: number;
  updated_by: number;
  created_at?: Date;
  updated_at?: Date;
}

export class RecipeSteps
  extends Model<RecipeStepsAttributes, never>
  implements RecipeStepsAttributes {
  id!: number;
  recipe_id!: number;
  item_id?: number;
  recipe_step_order!: number;
  recipe_step_description?: string;
  status!: RecipeStepsStatus;
  organization_id?: string;
  created_by!: number;
  updated_by!: number;
  created_at!: Date;
  updated_at!: Date;
}

RecipeSteps.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    recipe_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "mo_recipe",
        key: "id",
      },
      onDelete: "CASCADE",
      onUpdate: "CASCADE",
    },
    item_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: "nv_items",
        key: "id",
      },
      onDelete: "SET NULL",
      onUpdate: "CASCADE",
    },
    recipe_step_order: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    recipe_step_description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    status: {
      type: DataTypes.ENUM(Object.values(RecipeStepsStatus)),
      allowNull: false,
      defaultValue: RecipeStepsStatus.active,
    },
    organization_id: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
  },
  {
    sequelize,
    tableName: "mo_recipe_steps",
    modelName: "RecipeSteps",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    indexes: [
      {
        unique: true,
        fields: ["recipe_id", "recipe_step_order"],
        name: "unique_recipe_step_order",
      },
      {
        fields: ["organization_id"],
        name: "idx_recipe_steps_organization",
      },
      {
        fields: ["status"],
        name: "idx_recipe_steps_status",
      },
      {
        fields: ["created_by"],
        name: "idx_recipe_steps_created_by",
      },
      {
        fields: ["updated_by"],
        name: "idx_recipe_steps_updated_by",
      },
    ],
  }
);

// Define associations
RecipeSteps.associate = (models: any) => {
  // RecipeSteps belongs to Recipe
  RecipeSteps.belongsTo(models.Recipe, {
    foreignKey: "recipe_id",
    as: "recipe",
  });

  // RecipeSteps belongs to Item (item_id)
  RecipeSteps.belongsTo(models.Item, {
    foreignKey: "item_id",
    as: "stepItem",
    constraints: true,
    onDelete: "SET NULL",
    onUpdate: "CASCADE",
  });

  // RecipeSteps belongs to User (created_by)
  RecipeSteps.belongsTo(models.User, {
    foreignKey: "created_by",
    as: "creator",
  });

  // RecipeSteps belongs to User (updated_by)
  RecipeSteps.belongsTo(models.User, {
    foreignKey: "updated_by",
    as: "updater",
  });

  // RecipeSteps belongs to RecipeMaster (through recipe)
  // This allows querying steps across all versions of a recipe
  RecipeSteps.belongsTo(models.RecipeMaster, {
    foreignKey: "recipe_id",
    targetKey: "current_published_version_id",
    as: "masterRecipe",
    constraints: false, // No direct FK constraint
  });
};

export default RecipeSteps;
