{"version": 3, "file": "workbook-writer.js", "names": ["fs", "require", "Archiver", "StreamBuf", "RelType", "StylesXform", "SharedStrings", "DefinedNames", "CoreXform", "RelationshipsXform", "ContentTypesXform", "AppXform", "WorkbookXform", "SharedStringsXform", "WorksheetWriter", "theme1Xml", "WorkbookWriter", "constructor", "options", "created", "Date", "modified", "creator", "lastModifiedBy", "lastPrinted", "useSharedStrings", "sharedStrings", "styles", "useStyles", "<PERSON><PERSON>", "_definedNames", "_worksheets", "views", "zipOptions", "zip", "media", "commentRefs", "stream", "filename", "createWriteStream", "pipe", "promise", "Promise", "all", "addThemes", "addOfficeRels", "definedNames", "_openStream", "path", "bufSize", "batch", "append", "name", "on", "emit", "_commitWorksheets", "commitWorksheet", "worksheet", "committed", "resolve", "commit", "promises", "map", "length", "addMedia", "addContentTypes", "addApp", "addCore", "addSharedStrings", "addStyles", "addWorkbookRels", "addWorkbook", "_finalize", "nextId", "i", "addImage", "image", "id", "medium", "Object", "assign", "type", "extension", "push", "getImage", "addWorksheet", "undefined", "tabColor", "console", "trace", "properties", "workbook", "state", "pageSetup", "autoFilter", "headerFooter", "getWorksheet", "find", "xml", "xform", "toXml", "Id", "Type", "OfficeDocument", "Target", "CoreProperties", "ExtenderProperties", "model", "worksheets", "filter", "Boolean", "file", "buffer", "base64", "dataimg64", "content", "substring", "indexOf", "Error", "coreXform", "count", "sharedStringsXform", "relationships", "Styles", "Theme", "for<PERSON>ach", "rId", "Worksheet", "calcProperties", "prepare", "reject", "finalize", "module", "exports"], "sources": ["../../../../lib/stream/xlsx/workbook-writer.js"], "sourcesContent": ["const fs = require('fs');\nconst Archiver = require('archiver');\n\nconst StreamBuf = require('../../utils/stream-buf');\n\nconst RelType = require('../../xlsx/rel-type');\nconst StylesXform = require('../../xlsx/xform/style/styles-xform');\nconst SharedStrings = require('../../utils/shared-strings');\nconst DefinedNames = require('../../doc/defined-names');\n\nconst CoreXform = require('../../xlsx/xform/core/core-xform');\nconst RelationshipsXform = require('../../xlsx/xform/core/relationships-xform');\nconst ContentTypesXform = require('../../xlsx/xform/core/content-types-xform');\nconst AppXform = require('../../xlsx/xform/core/app-xform');\nconst WorkbookXform = require('../../xlsx/xform/book/workbook-xform');\nconst SharedStringsXform = require('../../xlsx/xform/strings/shared-strings-xform');\n\nconst WorksheetWriter = require('./worksheet-writer');\n\nconst theme1Xml = require('../../xlsx/xml/theme1.js');\n\nclass WorkbookWriter {\n  constructor(options) {\n    options = options || {};\n\n    this.created = options.created || new Date();\n    this.modified = options.modified || this.created;\n    this.creator = options.creator || 'ExcelJS';\n    this.lastModifiedBy = options.lastModifiedBy || 'ExcelJS';\n    this.lastPrinted = options.lastPrinted;\n\n    // using shared strings creates a smaller xlsx file but may use more memory\n    this.useSharedStrings = options.useSharedStrings || false;\n    this.sharedStrings = new SharedStrings();\n\n    // style manager\n    this.styles = options.useStyles ? new StylesXform(true) : new StylesXform.Mock(true);\n\n    // defined names\n    this._definedNames = new DefinedNames();\n\n    this._worksheets = [];\n    this.views = [];\n\n    this.zipOptions = options.zip;\n\n    this.media = [];\n    this.commentRefs = [];\n\n    this.zip = Archiver('zip', this.zipOptions);\n    if (options.stream) {\n      this.stream = options.stream;\n    } else if (options.filename) {\n      this.stream = fs.createWriteStream(options.filename);\n    } else {\n      this.stream = new StreamBuf();\n    }\n    this.zip.pipe(this.stream);\n\n    // these bits can be added right now\n    this.promise = Promise.all([this.addThemes(), this.addOfficeRels()]);\n  }\n\n  get definedNames() {\n    return this._definedNames;\n  }\n\n  _openStream(path) {\n    const stream = new StreamBuf({bufSize: 65536, batch: true});\n    this.zip.append(stream, {name: path});\n    stream.on('finish', () => {\n      stream.emit('zipped');\n    });\n    return stream;\n  }\n\n  _commitWorksheets() {\n    const commitWorksheet = function(worksheet) {\n      if (!worksheet.committed) {\n        return new Promise(resolve => {\n          worksheet.stream.on('zipped', () => {\n            resolve();\n          });\n          worksheet.commit();\n        });\n      }\n      return Promise.resolve();\n    };\n    // if there are any uncommitted worksheets, commit them now and wait\n    const promises = this._worksheets.map(commitWorksheet);\n    if (promises.length) {\n      return Promise.all(promises);\n    }\n    return Promise.resolve();\n  }\n\n  async commit() {\n    // commit all worksheets, then add suplimentary files\n    await this.promise;\n    await this.addMedia();\n    await this._commitWorksheets();\n    await Promise.all([\n      this.addContentTypes(),\n      this.addApp(),\n      this.addCore(),\n      this.addSharedStrings(),\n      this.addStyles(),\n      this.addWorkbookRels(),\n    ]);\n    await this.addWorkbook();\n    return this._finalize();\n  }\n\n  get nextId() {\n    // find the next unique spot to add worksheet\n    let i;\n    for (i = 1; i < this._worksheets.length; i++) {\n      if (!this._worksheets[i]) {\n        return i;\n      }\n    }\n    return this._worksheets.length || 1;\n  }\n\n  addImage(image) {\n    const id = this.media.length;\n    const medium = Object.assign({}, image, {type: 'image', name: `image${id}.${image.extension}`});\n    this.media.push(medium);\n    return id;\n  }\n\n  getImage(id) {\n    return this.media[id];\n  }\n\n  addWorksheet(name, options) {\n    // it's possible to add a worksheet with different than default\n    // shared string handling\n    // in fact, it's even possible to switch it mid-sheet\n    options = options || {};\n    const useSharedStrings =\n      options.useSharedStrings !== undefined ? options.useSharedStrings : this.useSharedStrings;\n\n    if (options.tabColor) {\n      // eslint-disable-next-line no-console\n      console.trace('tabColor option has moved to { properties: tabColor: {...} }');\n      options.properties = Object.assign(\n        {\n          tabColor: options.tabColor,\n        },\n        options.properties\n      );\n    }\n\n    const id = this.nextId;\n    name = name || `sheet${id}`;\n\n    const worksheet = new WorksheetWriter({\n      id,\n      name,\n      workbook: this,\n      useSharedStrings,\n      properties: options.properties,\n      state: options.state,\n      pageSetup: options.pageSetup,\n      views: options.views,\n      autoFilter: options.autoFilter,\n      headerFooter: options.headerFooter,\n    });\n\n    this._worksheets[id] = worksheet;\n    return worksheet;\n  }\n\n  getWorksheet(id) {\n    if (id === undefined) {\n      return this._worksheets.find(() => true);\n    }\n    if (typeof id === 'number') {\n      return this._worksheets[id];\n    }\n    if (typeof id === 'string') {\n      return this._worksheets.find(worksheet => worksheet && worksheet.name === id);\n    }\n    return undefined;\n  }\n\n  addStyles() {\n    return new Promise(resolve => {\n      this.zip.append(this.styles.xml, {name: 'xl/styles.xml'});\n      resolve();\n    });\n  }\n\n  addThemes() {\n    return new Promise(resolve => {\n      this.zip.append(theme1Xml, {name: 'xl/theme/theme1.xml'});\n      resolve();\n    });\n  }\n\n  addOfficeRels() {\n    return new Promise(resolve => {\n      const xform = new RelationshipsXform();\n      const xml = xform.toXml([\n        {Id: 'rId1', Type: RelType.OfficeDocument, Target: 'xl/workbook.xml'},\n        {Id: 'rId2', Type: RelType.CoreProperties, Target: 'docProps/core.xml'},\n        {Id: 'rId3', Type: RelType.ExtenderProperties, Target: 'docProps/app.xml'},\n      ]);\n      this.zip.append(xml, {name: '/_rels/.rels'});\n      resolve();\n    });\n  }\n\n  addContentTypes() {\n    return new Promise(resolve => {\n      const model = {\n        worksheets: this._worksheets.filter(Boolean),\n        sharedStrings: this.sharedStrings,\n        commentRefs: this.commentRefs,\n        media: this.media,\n      };\n      const xform = new ContentTypesXform();\n      const xml = xform.toXml(model);\n      this.zip.append(xml, {name: '[Content_Types].xml'});\n      resolve();\n    });\n  }\n\n  addMedia() {\n    return Promise.all(\n      this.media.map(medium => {\n        if (medium.type === 'image') {\n          const filename = `xl/media/${medium.name}`;\n          if (medium.filename) {\n            return this.zip.file(medium.filename, {name: filename});\n          }\n          if (medium.buffer) {\n            return this.zip.append(medium.buffer, {name: filename});\n          }\n          if (medium.base64) {\n            const dataimg64 = medium.base64;\n            const content = dataimg64.substring(dataimg64.indexOf(',') + 1);\n            return this.zip.append(content, {name: filename, base64: true});\n          }\n        }\n        throw new Error('Unsupported media');\n      })\n    );\n  }\n\n  addApp() {\n    return new Promise(resolve => {\n      const model = {\n        worksheets: this._worksheets.filter(Boolean),\n      };\n      const xform = new AppXform();\n      const xml = xform.toXml(model);\n      this.zip.append(xml, {name: 'docProps/app.xml'});\n      resolve();\n    });\n  }\n\n  addCore() {\n    return new Promise(resolve => {\n      const coreXform = new CoreXform();\n      const xml = coreXform.toXml(this);\n      this.zip.append(xml, {name: 'docProps/core.xml'});\n      resolve();\n    });\n  }\n\n  addSharedStrings() {\n    if (this.sharedStrings.count) {\n      return new Promise(resolve => {\n        const sharedStringsXform = new SharedStringsXform();\n        const xml = sharedStringsXform.toXml(this.sharedStrings);\n        this.zip.append(xml, {name: '/xl/sharedStrings.xml'});\n        resolve();\n      });\n    }\n    return Promise.resolve();\n  }\n\n  addWorkbookRels() {\n    let count = 1;\n    const relationships = [\n      {Id: `rId${count++}`, Type: RelType.Styles, Target: 'styles.xml'},\n      {Id: `rId${count++}`, Type: RelType.Theme, Target: 'theme/theme1.xml'},\n    ];\n    if (this.sharedStrings.count) {\n      relationships.push({\n        Id: `rId${count++}`,\n        Type: RelType.SharedStrings,\n        Target: 'sharedStrings.xml',\n      });\n    }\n    this._worksheets.forEach(worksheet => {\n      if (worksheet) {\n        worksheet.rId = `rId${count++}`;\n        relationships.push({\n          Id: worksheet.rId,\n          Type: RelType.Worksheet,\n          Target: `worksheets/sheet${worksheet.id}.xml`,\n        });\n      }\n    });\n    return new Promise(resolve => {\n      const xform = new RelationshipsXform();\n      const xml = xform.toXml(relationships);\n      this.zip.append(xml, {name: '/xl/_rels/workbook.xml.rels'});\n      resolve();\n    });\n  }\n\n  addWorkbook() {\n    const {zip} = this;\n    const model = {\n      worksheets: this._worksheets.filter(Boolean),\n      definedNames: this._definedNames.model,\n      views: this.views,\n      properties: {},\n      calcProperties: {},\n    };\n\n    return new Promise(resolve => {\n      const xform = new WorkbookXform();\n      xform.prepare(model);\n      zip.append(xform.toXml(model), {name: '/xl/workbook.xml'});\n      resolve();\n    });\n  }\n\n  _finalize() {\n    return new Promise((resolve, reject) => {\n      this.stream.on('error', reject);\n      this.stream.on('finish', () => {\n        resolve(this);\n      });\n      this.zip.on('error', reject);\n\n      this.zip.finalize();\n    });\n  }\n}\n\nmodule.exports = WorkbookWriter;\n"], "mappings": ";;AAAA,MAAMA,EAAE,GAAGC,OAAO,CAAC,IAAI,CAAC;AACxB,MAAMC,QAAQ,GAAGD,OAAO,CAAC,UAAU,CAAC;AAEpC,MAAME,SAAS,GAAGF,OAAO,CAAC,wBAAwB,CAAC;AAEnD,MAAMG,OAAO,GAAGH,OAAO,CAAC,qBAAqB,CAAC;AAC9C,MAAMI,WAAW,GAAGJ,OAAO,CAAC,qCAAqC,CAAC;AAClE,MAAMK,aAAa,GAAGL,OAAO,CAAC,4BAA4B,CAAC;AAC3D,MAAMM,YAAY,GAAGN,OAAO,CAAC,yBAAyB,CAAC;AAEvD,MAAMO,SAAS,GAAGP,OAAO,CAAC,kCAAkC,CAAC;AAC7D,MAAMQ,kBAAkB,GAAGR,OAAO,CAAC,2CAA2C,CAAC;AAC/E,MAAMS,iBAAiB,GAAGT,OAAO,CAAC,2CAA2C,CAAC;AAC9E,MAAMU,QAAQ,GAAGV,OAAO,CAAC,iCAAiC,CAAC;AAC3D,MAAMW,aAAa,GAAGX,OAAO,CAAC,sCAAsC,CAAC;AACrE,MAAMY,kBAAkB,GAAGZ,OAAO,CAAC,+CAA+C,CAAC;AAEnF,MAAMa,eAAe,GAAGb,OAAO,CAAC,oBAAoB,CAAC;AAErD,MAAMc,SAAS,GAAGd,OAAO,CAAC,0BAA0B,CAAC;AAErD,MAAMe,cAAc,CAAC;EACnBC,WAAWA,CAACC,OAAO,EAAE;IACnBA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;IAEvB,IAAI,CAACC,OAAO,GAAGD,OAAO,CAACC,OAAO,IAAI,IAAIC,IAAI,CAAC,CAAC;IAC5C,IAAI,CAACC,QAAQ,GAAGH,OAAO,CAACG,QAAQ,IAAI,IAAI,CAACF,OAAO;IAChD,IAAI,CAACG,OAAO,GAAGJ,OAAO,CAACI,OAAO,IAAI,SAAS;IAC3C,IAAI,CAACC,cAAc,GAAGL,OAAO,CAACK,cAAc,IAAI,SAAS;IACzD,IAAI,CAACC,WAAW,GAAGN,OAAO,CAACM,WAAW;;IAEtC;IACA,IAAI,CAACC,gBAAgB,GAAGP,OAAO,CAACO,gBAAgB,IAAI,KAAK;IACzD,IAAI,CAACC,aAAa,GAAG,IAAIpB,aAAa,CAAC,CAAC;;IAExC;IACA,IAAI,CAACqB,MAAM,GAAGT,OAAO,CAACU,SAAS,GAAG,IAAIvB,WAAW,CAAC,IAAI,CAAC,GAAG,IAAIA,WAAW,CAACwB,IAAI,CAAC,IAAI,CAAC;;IAEpF;IACA,IAAI,CAACC,aAAa,GAAG,IAAIvB,YAAY,CAAC,CAAC;IAEvC,IAAI,CAACwB,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,KAAK,GAAG,EAAE;IAEf,IAAI,CAACC,UAAU,GAAGf,OAAO,CAACgB,GAAG;IAE7B,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,WAAW,GAAG,EAAE;IAErB,IAAI,CAACF,GAAG,GAAGhC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC+B,UAAU,CAAC;IAC3C,IAAIf,OAAO,CAACmB,MAAM,EAAE;MAClB,IAAI,CAACA,MAAM,GAAGnB,OAAO,CAACmB,MAAM;IAC9B,CAAC,MAAM,IAAInB,OAAO,CAACoB,QAAQ,EAAE;MAC3B,IAAI,CAACD,MAAM,GAAGrC,EAAE,CAACuC,iBAAiB,CAACrB,OAAO,CAACoB,QAAQ,CAAC;IACtD,CAAC,MAAM;MACL,IAAI,CAACD,MAAM,GAAG,IAAIlC,SAAS,CAAC,CAAC;IAC/B;IACA,IAAI,CAAC+B,GAAG,CAACM,IAAI,CAAC,IAAI,CAACH,MAAM,CAAC;;IAE1B;IACA,IAAI,CAACI,OAAO,GAAGC,OAAO,CAACC,GAAG,CAAC,CAAC,IAAI,CAACC,SAAS,CAAC,CAAC,EAAE,IAAI,CAACC,aAAa,CAAC,CAAC,CAAC,CAAC;EACtE;EAEA,IAAIC,YAAYA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAChB,aAAa;EAC3B;EAEAiB,WAAWA,CAACC,IAAI,EAAE;IAChB,MAAMX,MAAM,GAAG,IAAIlC,SAAS,CAAC;MAAC8C,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAI,CAAC,CAAC;IAC3D,IAAI,CAAChB,GAAG,CAACiB,MAAM,CAACd,MAAM,EAAE;MAACe,IAAI,EAAEJ;IAAI,CAAC,CAAC;IACrCX,MAAM,CAACgB,EAAE,CAAC,QAAQ,EAAE,MAAM;MACxBhB,MAAM,CAACiB,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC,CAAC;IACF,OAAOjB,MAAM;EACf;EAEAkB,iBAAiBA,CAAA,EAAG;IAClB,MAAMC,eAAe,GAAG,SAAAA,CAASC,SAAS,EAAE;MAC1C,IAAI,CAACA,SAAS,CAACC,SAAS,EAAE;QACxB,OAAO,IAAIhB,OAAO,CAACiB,OAAO,IAAI;UAC5BF,SAAS,CAACpB,MAAM,CAACgB,EAAE,CAAC,QAAQ,EAAE,MAAM;YAClCM,OAAO,CAAC,CAAC;UACX,CAAC,CAAC;UACFF,SAAS,CAACG,MAAM,CAAC,CAAC;QACpB,CAAC,CAAC;MACJ;MACA,OAAOlB,OAAO,CAACiB,OAAO,CAAC,CAAC;IAC1B,CAAC;IACD;IACA,MAAME,QAAQ,GAAG,IAAI,CAAC9B,WAAW,CAAC+B,GAAG,CAACN,eAAe,CAAC;IACtD,IAAIK,QAAQ,CAACE,MAAM,EAAE;MACnB,OAAOrB,OAAO,CAACC,GAAG,CAACkB,QAAQ,CAAC;IAC9B;IACA,OAAOnB,OAAO,CAACiB,OAAO,CAAC,CAAC;EAC1B;EAEA,MAAMC,MAAMA,CAAA,EAAG;IACb;IACA,MAAM,IAAI,CAACnB,OAAO;IAClB,MAAM,IAAI,CAACuB,QAAQ,CAAC,CAAC;IACrB,MAAM,IAAI,CAACT,iBAAiB,CAAC,CAAC;IAC9B,MAAMb,OAAO,CAACC,GAAG,CAAC,CAChB,IAAI,CAACsB,eAAe,CAAC,CAAC,EACtB,IAAI,CAACC,MAAM,CAAC,CAAC,EACb,IAAI,CAACC,OAAO,CAAC,CAAC,EACd,IAAI,CAACC,gBAAgB,CAAC,CAAC,EACvB,IAAI,CAACC,SAAS,CAAC,CAAC,EAChB,IAAI,CAACC,eAAe,CAAC,CAAC,CACvB,CAAC;IACF,MAAM,IAAI,CAACC,WAAW,CAAC,CAAC;IACxB,OAAO,IAAI,CAACC,SAAS,CAAC,CAAC;EACzB;EAEA,IAAIC,MAAMA,CAAA,EAAG;IACX;IACA,IAAIC,CAAC;IACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC3C,WAAW,CAACgC,MAAM,EAAEW,CAAC,EAAE,EAAE;MAC5C,IAAI,CAAC,IAAI,CAAC3C,WAAW,CAAC2C,CAAC,CAAC,EAAE;QACxB,OAAOA,CAAC;MACV;IACF;IACA,OAAO,IAAI,CAAC3C,WAAW,CAACgC,MAAM,IAAI,CAAC;EACrC;EAEAY,QAAQA,CAACC,KAAK,EAAE;IACd,MAAMC,EAAE,GAAG,IAAI,CAAC1C,KAAK,CAAC4B,MAAM;IAC5B,MAAMe,MAAM,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEJ,KAAK,EAAE;MAACK,IAAI,EAAE,OAAO;MAAE7B,IAAI,EAAG,QAAOyB,EAAG,IAAGD,KAAK,CAACM,SAAU;IAAC,CAAC,CAAC;IAC/F,IAAI,CAAC/C,KAAK,CAACgD,IAAI,CAACL,MAAM,CAAC;IACvB,OAAOD,EAAE;EACX;EAEAO,QAAQA,CAACP,EAAE,EAAE;IACX,OAAO,IAAI,CAAC1C,KAAK,CAAC0C,EAAE,CAAC;EACvB;EAEAQ,YAAYA,CAACjC,IAAI,EAAElC,OAAO,EAAE;IAC1B;IACA;IACA;IACAA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;IACvB,MAAMO,gBAAgB,GACpBP,OAAO,CAACO,gBAAgB,KAAK6D,SAAS,GAAGpE,OAAO,CAACO,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;IAE3F,IAAIP,OAAO,CAACqE,QAAQ,EAAE;MACpB;MACAC,OAAO,CAACC,KAAK,CAAC,8DAA8D,CAAC;MAC7EvE,OAAO,CAACwE,UAAU,GAAGX,MAAM,CAACC,MAAM,CAChC;QACEO,QAAQ,EAAErE,OAAO,CAACqE;MACpB,CAAC,EACDrE,OAAO,CAACwE,UACV,CAAC;IACH;IAEA,MAAMb,EAAE,GAAG,IAAI,CAACJ,MAAM;IACtBrB,IAAI,GAAGA,IAAI,IAAK,QAAOyB,EAAG,EAAC;IAE3B,MAAMpB,SAAS,GAAG,IAAI3C,eAAe,CAAC;MACpC+D,EAAE;MACFzB,IAAI;MACJuC,QAAQ,EAAE,IAAI;MACdlE,gBAAgB;MAChBiE,UAAU,EAAExE,OAAO,CAACwE,UAAU;MAC9BE,KAAK,EAAE1E,OAAO,CAAC0E,KAAK;MACpBC,SAAS,EAAE3E,OAAO,CAAC2E,SAAS;MAC5B7D,KAAK,EAAEd,OAAO,CAACc,KAAK;MACpB8D,UAAU,EAAE5E,OAAO,CAAC4E,UAAU;MAC9BC,YAAY,EAAE7E,OAAO,CAAC6E;IACxB,CAAC,CAAC;IAEF,IAAI,CAAChE,WAAW,CAAC8C,EAAE,CAAC,GAAGpB,SAAS;IAChC,OAAOA,SAAS;EAClB;EAEAuC,YAAYA,CAACnB,EAAE,EAAE;IACf,IAAIA,EAAE,KAAKS,SAAS,EAAE;MACpB,OAAO,IAAI,CAACvD,WAAW,CAACkE,IAAI,CAAC,MAAM,IAAI,CAAC;IAC1C;IACA,IAAI,OAAOpB,EAAE,KAAK,QAAQ,EAAE;MAC1B,OAAO,IAAI,CAAC9C,WAAW,CAAC8C,EAAE,CAAC;IAC7B;IACA,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE;MAC1B,OAAO,IAAI,CAAC9C,WAAW,CAACkE,IAAI,CAACxC,SAAS,IAAIA,SAAS,IAAIA,SAAS,CAACL,IAAI,KAAKyB,EAAE,CAAC;IAC/E;IACA,OAAOS,SAAS;EAClB;EAEAjB,SAASA,CAAA,EAAG;IACV,OAAO,IAAI3B,OAAO,CAACiB,OAAO,IAAI;MAC5B,IAAI,CAACzB,GAAG,CAACiB,MAAM,CAAC,IAAI,CAACxB,MAAM,CAACuE,GAAG,EAAE;QAAC9C,IAAI,EAAE;MAAe,CAAC,CAAC;MACzDO,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;EACJ;EAEAf,SAASA,CAAA,EAAG;IACV,OAAO,IAAIF,OAAO,CAACiB,OAAO,IAAI;MAC5B,IAAI,CAACzB,GAAG,CAACiB,MAAM,CAACpC,SAAS,EAAE;QAACqC,IAAI,EAAE;MAAqB,CAAC,CAAC;MACzDO,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;EACJ;EAEAd,aAAaA,CAAA,EAAG;IACd,OAAO,IAAIH,OAAO,CAACiB,OAAO,IAAI;MAC5B,MAAMwC,KAAK,GAAG,IAAI1F,kBAAkB,CAAC,CAAC;MACtC,MAAMyF,GAAG,GAAGC,KAAK,CAACC,KAAK,CAAC,CACtB;QAACC,EAAE,EAAE,MAAM;QAAEC,IAAI,EAAElG,OAAO,CAACmG,cAAc;QAAEC,MAAM,EAAE;MAAiB,CAAC,EACrE;QAACH,EAAE,EAAE,MAAM;QAAEC,IAAI,EAAElG,OAAO,CAACqG,cAAc;QAAED,MAAM,EAAE;MAAmB,CAAC,EACvE;QAACH,EAAE,EAAE,MAAM;QAAEC,IAAI,EAAElG,OAAO,CAACsG,kBAAkB;QAAEF,MAAM,EAAE;MAAkB,CAAC,CAC3E,CAAC;MACF,IAAI,CAACtE,GAAG,CAACiB,MAAM,CAAC+C,GAAG,EAAE;QAAC9C,IAAI,EAAE;MAAc,CAAC,CAAC;MAC5CO,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;EACJ;EAEAM,eAAeA,CAAA,EAAG;IAChB,OAAO,IAAIvB,OAAO,CAACiB,OAAO,IAAI;MAC5B,MAAMgD,KAAK,GAAG;QACZC,UAAU,EAAE,IAAI,CAAC7E,WAAW,CAAC8E,MAAM,CAACC,OAAO,CAAC;QAC5CpF,aAAa,EAAE,IAAI,CAACA,aAAa;QACjCU,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BD,KAAK,EAAE,IAAI,CAACA;MACd,CAAC;MACD,MAAMgE,KAAK,GAAG,IAAIzF,iBAAiB,CAAC,CAAC;MACrC,MAAMwF,GAAG,GAAGC,KAAK,CAACC,KAAK,CAACO,KAAK,CAAC;MAC9B,IAAI,CAACzE,GAAG,CAACiB,MAAM,CAAC+C,GAAG,EAAE;QAAC9C,IAAI,EAAE;MAAqB,CAAC,CAAC;MACnDO,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;EACJ;EAEAK,QAAQA,CAAA,EAAG;IACT,OAAOtB,OAAO,CAACC,GAAG,CAChB,IAAI,CAACR,KAAK,CAAC2B,GAAG,CAACgB,MAAM,IAAI;MACvB,IAAIA,MAAM,CAACG,IAAI,KAAK,OAAO,EAAE;QAC3B,MAAM3C,QAAQ,GAAI,YAAWwC,MAAM,CAAC1B,IAAK,EAAC;QAC1C,IAAI0B,MAAM,CAACxC,QAAQ,EAAE;UACnB,OAAO,IAAI,CAACJ,GAAG,CAAC6E,IAAI,CAACjC,MAAM,CAACxC,QAAQ,EAAE;YAACc,IAAI,EAAEd;UAAQ,CAAC,CAAC;QACzD;QACA,IAAIwC,MAAM,CAACkC,MAAM,EAAE;UACjB,OAAO,IAAI,CAAC9E,GAAG,CAACiB,MAAM,CAAC2B,MAAM,CAACkC,MAAM,EAAE;YAAC5D,IAAI,EAAEd;UAAQ,CAAC,CAAC;QACzD;QACA,IAAIwC,MAAM,CAACmC,MAAM,EAAE;UACjB,MAAMC,SAAS,GAAGpC,MAAM,CAACmC,MAAM;UAC/B,MAAME,OAAO,GAAGD,SAAS,CAACE,SAAS,CAACF,SAAS,CAACG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;UAC/D,OAAO,IAAI,CAACnF,GAAG,CAACiB,MAAM,CAACgE,OAAO,EAAE;YAAC/D,IAAI,EAAEd,QAAQ;YAAE2E,MAAM,EAAE;UAAI,CAAC,CAAC;QACjE;MACF;MACA,MAAM,IAAIK,KAAK,CAAC,mBAAmB,CAAC;IACtC,CAAC,CACH,CAAC;EACH;EAEApD,MAAMA,CAAA,EAAG;IACP,OAAO,IAAIxB,OAAO,CAACiB,OAAO,IAAI;MAC5B,MAAMgD,KAAK,GAAG;QACZC,UAAU,EAAE,IAAI,CAAC7E,WAAW,CAAC8E,MAAM,CAACC,OAAO;MAC7C,CAAC;MACD,MAAMX,KAAK,GAAG,IAAIxF,QAAQ,CAAC,CAAC;MAC5B,MAAMuF,GAAG,GAAGC,KAAK,CAACC,KAAK,CAACO,KAAK,CAAC;MAC9B,IAAI,CAACzE,GAAG,CAACiB,MAAM,CAAC+C,GAAG,EAAE;QAAC9C,IAAI,EAAE;MAAkB,CAAC,CAAC;MAChDO,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;EACJ;EAEAQ,OAAOA,CAAA,EAAG;IACR,OAAO,IAAIzB,OAAO,CAACiB,OAAO,IAAI;MAC5B,MAAM4D,SAAS,GAAG,IAAI/G,SAAS,CAAC,CAAC;MACjC,MAAM0F,GAAG,GAAGqB,SAAS,CAACnB,KAAK,CAAC,IAAI,CAAC;MACjC,IAAI,CAAClE,GAAG,CAACiB,MAAM,CAAC+C,GAAG,EAAE;QAAC9C,IAAI,EAAE;MAAmB,CAAC,CAAC;MACjDO,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;EACJ;EAEAS,gBAAgBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAAC1C,aAAa,CAAC8F,KAAK,EAAE;MAC5B,OAAO,IAAI9E,OAAO,CAACiB,OAAO,IAAI;QAC5B,MAAM8D,kBAAkB,GAAG,IAAI5G,kBAAkB,CAAC,CAAC;QACnD,MAAMqF,GAAG,GAAGuB,kBAAkB,CAACrB,KAAK,CAAC,IAAI,CAAC1E,aAAa,CAAC;QACxD,IAAI,CAACQ,GAAG,CAACiB,MAAM,CAAC+C,GAAG,EAAE;UAAC9C,IAAI,EAAE;QAAuB,CAAC,CAAC;QACrDO,OAAO,CAAC,CAAC;MACX,CAAC,CAAC;IACJ;IACA,OAAOjB,OAAO,CAACiB,OAAO,CAAC,CAAC;EAC1B;EAEAW,eAAeA,CAAA,EAAG;IAChB,IAAIkD,KAAK,GAAG,CAAC;IACb,MAAME,aAAa,GAAG,CACpB;MAACrB,EAAE,EAAG,MAAKmB,KAAK,EAAG,EAAC;MAAElB,IAAI,EAAElG,OAAO,CAACuH,MAAM;MAAEnB,MAAM,EAAE;IAAY,CAAC,EACjE;MAACH,EAAE,EAAG,MAAKmB,KAAK,EAAG,EAAC;MAAElB,IAAI,EAAElG,OAAO,CAACwH,KAAK;MAAEpB,MAAM,EAAE;IAAkB,CAAC,CACvE;IACD,IAAI,IAAI,CAAC9E,aAAa,CAAC8F,KAAK,EAAE;MAC5BE,aAAa,CAACvC,IAAI,CAAC;QACjBkB,EAAE,EAAG,MAAKmB,KAAK,EAAG,EAAC;QACnBlB,IAAI,EAAElG,OAAO,CAACE,aAAa;QAC3BkG,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;IACA,IAAI,CAACzE,WAAW,CAAC8F,OAAO,CAACpE,SAAS,IAAI;MACpC,IAAIA,SAAS,EAAE;QACbA,SAAS,CAACqE,GAAG,GAAI,MAAKN,KAAK,EAAG,EAAC;QAC/BE,aAAa,CAACvC,IAAI,CAAC;UACjBkB,EAAE,EAAE5C,SAAS,CAACqE,GAAG;UACjBxB,IAAI,EAAElG,OAAO,CAAC2H,SAAS;UACvBvB,MAAM,EAAG,mBAAkB/C,SAAS,CAACoB,EAAG;QAC1C,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACF,OAAO,IAAInC,OAAO,CAACiB,OAAO,IAAI;MAC5B,MAAMwC,KAAK,GAAG,IAAI1F,kBAAkB,CAAC,CAAC;MACtC,MAAMyF,GAAG,GAAGC,KAAK,CAACC,KAAK,CAACsB,aAAa,CAAC;MACtC,IAAI,CAACxF,GAAG,CAACiB,MAAM,CAAC+C,GAAG,EAAE;QAAC9C,IAAI,EAAE;MAA6B,CAAC,CAAC;MAC3DO,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;EACJ;EAEAY,WAAWA,CAAA,EAAG;IACZ,MAAM;MAACrC;IAAG,CAAC,GAAG,IAAI;IAClB,MAAMyE,KAAK,GAAG;MACZC,UAAU,EAAE,IAAI,CAAC7E,WAAW,CAAC8E,MAAM,CAACC,OAAO,CAAC;MAC5ChE,YAAY,EAAE,IAAI,CAAChB,aAAa,CAAC6E,KAAK;MACtC3E,KAAK,EAAE,IAAI,CAACA,KAAK;MACjB0D,UAAU,EAAE,CAAC,CAAC;MACdsC,cAAc,EAAE,CAAC;IACnB,CAAC;IAED,OAAO,IAAItF,OAAO,CAACiB,OAAO,IAAI;MAC5B,MAAMwC,KAAK,GAAG,IAAIvF,aAAa,CAAC,CAAC;MACjCuF,KAAK,CAAC8B,OAAO,CAACtB,KAAK,CAAC;MACpBzE,GAAG,CAACiB,MAAM,CAACgD,KAAK,CAACC,KAAK,CAACO,KAAK,CAAC,EAAE;QAACvD,IAAI,EAAE;MAAkB,CAAC,CAAC;MAC1DO,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;EACJ;EAEAa,SAASA,CAAA,EAAG;IACV,OAAO,IAAI9B,OAAO,CAAC,CAACiB,OAAO,EAAEuE,MAAM,KAAK;MACtC,IAAI,CAAC7F,MAAM,CAACgB,EAAE,CAAC,OAAO,EAAE6E,MAAM,CAAC;MAC/B,IAAI,CAAC7F,MAAM,CAACgB,EAAE,CAAC,QAAQ,EAAE,MAAM;QAC7BM,OAAO,CAAC,IAAI,CAAC;MACf,CAAC,CAAC;MACF,IAAI,CAACzB,GAAG,CAACmB,EAAE,CAAC,OAAO,EAAE6E,MAAM,CAAC;MAE5B,IAAI,CAAChG,GAAG,CAACiG,QAAQ,CAAC,CAAC;IACrB,CAAC,CAAC;EACJ;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGrH,cAAc"}