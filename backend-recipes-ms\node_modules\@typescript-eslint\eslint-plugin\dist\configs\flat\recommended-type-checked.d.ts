import type { FlatConfig } from '@typescript-eslint/utils/ts-eslint';
/**
 * Contains all of `recommended` along with additional recommended rules that require type information.
 * @see {@link https://typescript-eslint.io/users/configs#recommended-type-checked}
 */
declare const _default: (plugin: FlatConfig.Plugin, parser: FlatConfig.Parser) => FlatConfig.ConfigArray;
export default _default;
//# sourceMappingURL=recommended-type-checked.d.ts.map