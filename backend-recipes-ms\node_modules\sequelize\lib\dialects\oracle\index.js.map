{"version": 3, "sources": ["../../../src/dialects/oracle/index.js"], "sourcesContent": ["// Copyright (c) 2022, Oracle and/or its affiliates. All rights reserved\n\n'use strict';\n\nconst _ = require('lodash');\nconst { AbstractDialect } = require('../abstract');\nconst { OracleConnectionManager } = require('./connection-manager');\nconst { OracleQuery } = require('./query');\nconst { OracleQueryGenerator } = require('./query-generator');\nconst DataTypes = require('../../data-types').oracle;\nconst { OracleQueryInterface } = require('./query-interface');\n\nclass OracleDialect extends AbstractDialect {\n  constructor(sequelize) {\n    super();\n    this.sequelize = sequelize;\n    this.connectionManager = new OracleConnectionManager(this, sequelize);\n    this.connectionManager.initPools();\n    this.queryGenerator = new OracleQueryGenerator({\n      _dialect: this,\n      sequelize\n    });\n    this.queryInterface = new OracleQueryInterface(sequelize, this.queryGenerator);\n  }\n}\n\nOracleDialect.prototype.supports = _.merge(_.cloneDeep(AbstractDialect.prototype.supports), {\n  'VALUES ()': true,\n  'LIMIT ON UPDATE': true,\n  IGNORE: ' IGNORE',\n  lock: true,\n  lockOuterJoinFailure: true,\n  forShare: 'FOR UPDATE',\n  skipLocked: true,\n  index: {\n    collate: false,\n    length: false,\n    parser: false,\n    type: false,\n    using: false\n  },\n  constraints: {\n    restrict: false\n  },\n  returnValues: false,\n  returnIntoValues: true,\n  'ORDER NULLS': true,\n  schemas: true,\n  updateOnDuplicate: false,\n  indexViaAlter: false,\n  NUMERIC: true,\n  JSON: true,\n  upserts: true,\n  bulkDefault: true,\n  topLevelOrderByRequired: true,\n  GEOMETRY: false\n});\n\nOracleDialect.prototype.defaultVersion = '18.0.0';\nOracleDialect.prototype.Query = OracleQuery;\nOracleDialect.prototype.queryGenerator = OracleQueryGenerator;\nOracleDialect.prototype.DataTypes = DataTypes;\nOracleDialect.prototype.name = 'oracle';\nOracleDialect.prototype.TICK_CHAR = '\"';\nOracleDialect.prototype.TICK_CHAR_LEFT = OracleDialect.prototype.TICK_CHAR;\nOracleDialect.prototype.TICK_CHAR_RIGHT = OracleDialect.prototype.TICK_CHAR;\n\nmodule.exports = OracleDialect;\n"], "mappings": ";AAIA,MAAM,IAAI,QAAQ;AAClB,MAAM,EAAE,oBAAoB,QAAQ;AACpC,MAAM,EAAE,4BAA4B,QAAQ;AAC5C,MAAM,EAAE,gBAAgB,QAAQ;AAChC,MAAM,EAAE,yBAAyB,QAAQ;AACzC,MAAM,YAAY,QAAQ,oBAAoB;AAC9C,MAAM,EAAE,yBAAyB,QAAQ;AAEzC,4BAA4B,gBAAgB;AAAA,EAC1C,YAAY,WAAW;AACrB;AACA,SAAK,YAAY;AACjB,SAAK,oBAAoB,IAAI,wBAAwB,MAAM;AAC3D,SAAK,kBAAkB;AACvB,SAAK,iBAAiB,IAAI,qBAAqB;AAAA,MAC7C,UAAU;AAAA,MACV;AAAA;AAEF,SAAK,iBAAiB,IAAI,qBAAqB,WAAW,KAAK;AAAA;AAAA;AAInE,cAAc,UAAU,WAAW,EAAE,MAAM,EAAE,UAAU,gBAAgB,UAAU,WAAW;AAAA,EAC1F,aAAa;AAAA,EACb,mBAAmB;AAAA,EACnB,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,sBAAsB;AAAA,EACtB,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,OAAO;AAAA,IACL,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,OAAO;AAAA;AAAA,EAET,aAAa;AAAA,IACX,UAAU;AAAA;AAAA,EAEZ,cAAc;AAAA,EACd,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,SAAS;AAAA,EACT,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,SAAS;AAAA,EACT,MAAM;AAAA,EACN,SAAS;AAAA,EACT,aAAa;AAAA,EACb,yBAAyB;AAAA,EACzB,UAAU;AAAA;AAGZ,cAAc,UAAU,iBAAiB;AACzC,cAAc,UAAU,QAAQ;AAChC,cAAc,UAAU,iBAAiB;AACzC,cAAc,UAAU,YAAY;AACpC,cAAc,UAAU,OAAO;AAC/B,cAAc,UAAU,YAAY;AACpC,cAAc,UAAU,iBAAiB,cAAc,UAAU;AACjE,cAAc,UAAU,kBAAkB,cAAc,UAAU;AAElE,OAAO,UAAU;", "names": []}