'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    console.log('🚀 Adding versioning columns to mo_recipe table...');
    
    // Add versioning columns to mo_recipe table
    await queryInterface.addColumn('mo_recipe', 'master_recipe_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'mo_recipe_master',
        key: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
      comment: 'Reference to the master recipe this version belongs to',
    });

    await queryInterface.addColumn('mo_recipe', 'version_number', {
      type: Sequelize.INTEGER,
      allowNull: true,
      comment: 'Version number for this recipe (1, 2, 3, etc.)',
    });

    await queryInterface.addColumn('mo_recipe', 'version_status', {
      type: Sequelize.ENUM('draft', 'published', 'revised', 'archived'),
      allowNull: true,
      comment: 'Status of this specific version (draft, published, revised, archived)',
    });

    await queryInterface.addColumn('mo_recipe', 'is_current_version', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: 'Whether this is the current active version for its status',
    });

    await queryInterface.addColumn('mo_recipe', 'parent_version_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'mo_recipe',
        key: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
      comment: 'Reference to the parent version this was created from',
    });

    await queryInterface.addColumn('mo_recipe', 'version_created_at', {
      type: Sequelize.DATE,
      allowNull: true,
      comment: 'When this version was created',
    });

    await queryInterface.addColumn('mo_recipe', 'version_published_at', {
      type: Sequelize.DATE,
      allowNull: true,
      comment: 'When this version was published (if applicable)',
    });

    await queryInterface.addColumn('mo_recipe', 'version_archived_at', {
      type: Sequelize.DATE,
      allowNull: true,
      comment: 'When this version was archived (if applicable)',
    });

    await queryInterface.addColumn('mo_recipe', 'version_notes', {
      type: Sequelize.TEXT,
      allowNull: true,
      comment: 'Notes about this version (changes made, etc.)',
    });

    console.log('✅ Versioning columns added to mo_recipe table successfully');
  },

  async down(queryInterface, Sequelize) {
    console.log('🗑️  Removing versioning columns from mo_recipe table...');
    
    // Remove versioning columns in reverse order
    await queryInterface.removeColumn('mo_recipe', 'version_notes');
    await queryInterface.removeColumn('mo_recipe', 'version_archived_at');
    await queryInterface.removeColumn('mo_recipe', 'version_published_at');
    await queryInterface.removeColumn('mo_recipe', 'version_created_at');
    await queryInterface.removeColumn('mo_recipe', 'parent_version_id');
    await queryInterface.removeColumn('mo_recipe', 'is_current_version');
    await queryInterface.removeColumn('mo_recipe', 'version_status');
    await queryInterface.removeColumn('mo_recipe', 'version_number');
    await queryInterface.removeColumn('mo_recipe', 'master_recipe_id');

    console.log('✅ Versioning columns removed from mo_recipe table successfully');
  }
};
