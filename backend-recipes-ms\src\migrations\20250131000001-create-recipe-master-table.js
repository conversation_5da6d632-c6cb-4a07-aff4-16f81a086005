'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    console.log('🚀 Creating mo_recipe_master table...');
    
    await queryInterface.createTable('mo_recipe_master', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      master_recipe_slug: {
        type: Sequelize.STRING(100),
        allowNull: false,
        unique: true,
        comment: 'Unique slug for the master recipe across all versions',
      },
      master_recipe_title: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: 'Master title for the recipe (used for identification)',
      },
      organization_id: {
        type: Sequelize.STRING(100),
        allowNull: true,
        comment: 'Organization ID for multi-tenant support',
      },
      current_published_version_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: 'Reference to the currently published version',
      },
      current_draft_version_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: 'Reference to the current draft version (if any)',
      },
      total_versions: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: 'Total number of versions created for this recipe',
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: 'Whether this master recipe is active',
      },
      created_by: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: 'User ID who created the master recipe',
      },
      updated_by: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: 'User ID who last updated the master recipe',
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
      },
    });

    console.log('✅ mo_recipe_master table created successfully');
  },

  async down(queryInterface, Sequelize) {
    console.log('🗑️  Dropping mo_recipe_master table...');
    await queryInterface.dropTable('mo_recipe_master');
    console.log('✅ mo_recipe_master table dropped successfully');
  }
};
