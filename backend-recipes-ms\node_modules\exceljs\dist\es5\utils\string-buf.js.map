{"version": 3, "file": "string-buf.js", "names": ["StringBuf", "constructor", "options", "_buf", "<PERSON><PERSON><PERSON>", "alloc", "size", "_encoding", "encoding", "_inPos", "_buffer", "undefined", "length", "capacity", "buffer", "<PERSON><PERSON><PERSON><PERSON>", "copy", "reset", "position", "_grow", "min", "buf", "addText", "text", "inPos", "write", "addStringBuf", "inBuf", "module", "exports"], "sources": ["../../../lib/utils/string-buf.js"], "sourcesContent": ["// StringBuf - a way to keep string memory operations to a minimum\n// while building the strings for the xml files\nclass StringBuf {\n  constructor(options) {\n    this._buf = Buffer.alloc((options && options.size) || 16384);\n    this._encoding = (options && options.encoding) || 'utf8';\n\n    // where in the buffer we are at\n    this._inPos = 0;\n\n    // for use by to<PERSON>uff<PERSON>()\n    this._buffer = undefined;\n  }\n\n  get length() {\n    return this._inPos;\n  }\n\n  get capacity() {\n    return this._buf.length;\n  }\n\n  get buffer() {\n    return this._buf;\n  }\n\n  toBuffer() {\n    // return the current data as a single enclosing buffer\n    if (!this._buffer) {\n      this._buffer = Buffer.alloc(this.length);\n      this._buf.copy(this._buffer, 0, 0, this.length);\n    }\n    return this._buffer;\n  }\n\n  reset(position) {\n    position = position || 0;\n    this._buffer = undefined;\n    this._inPos = position;\n  }\n\n  _grow(min) {\n    let size = this._buf.length * 2;\n    while (size < min) {\n      size *= 2;\n    }\n    const buf = Buffer.alloc(size);\n    this._buf.copy(buf, 0);\n    this._buf = buf;\n  }\n\n  addText(text) {\n    this._buffer = undefined;\n\n    let inPos = this._inPos + this._buf.write(text, this._inPos, this._encoding);\n\n    // if we've hit (or nearing capacity), grow the buf\n    while (inPos >= this._buf.length - 4) {\n      this._grow(this._inPos + text.length);\n\n      // keep trying to write until we've completely written the text\n      inPos = this._inPos + this._buf.write(text, this._inPos, this._encoding);\n    }\n\n    this._inPos = inPos;\n  }\n\n  addStringBuf(inBuf) {\n    if (inBuf.length) {\n      this._buffer = undefined;\n\n      if (this.length + inBuf.length > this.capacity) {\n        this._grow(this.length + inBuf.length);\n      }\n      // eslint-disable-next-line no-underscore-dangle\n      inBuf._buf.copy(this._buf, this._inPos, 0, inBuf.length);\n      this._inPos += inBuf.length;\n    }\n  }\n}\n\nmodule.exports = StringBuf;\n"], "mappings": ";;AAAA;AACA;AACA,MAAMA,SAAS,CAAC;EACdC,WAAWA,CAACC,OAAO,EAAE;IACnB,IAAI,CAACC,IAAI,GAAGC,MAAM,CAACC,KAAK,CAAEH,OAAO,IAAIA,OAAO,CAACI,IAAI,IAAK,KAAK,CAAC;IAC5D,IAAI,CAACC,SAAS,GAAIL,OAAO,IAAIA,OAAO,CAACM,QAAQ,IAAK,MAAM;;IAExD;IACA,IAAI,CAACC,MAAM,GAAG,CAAC;;IAEf;IACA,IAAI,CAACC,OAAO,GAAGC,SAAS;EAC1B;EAEA,IAAIC,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAACH,MAAM;EACpB;EAEA,IAAII,QAAQA,CAAA,EAAG;IACb,OAAO,IAAI,CAACV,IAAI,CAACS,MAAM;EACzB;EAEA,IAAIE,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAACX,IAAI;EAClB;EAEAY,QAAQA,CAAA,EAAG;IACT;IACA,IAAI,CAAC,IAAI,CAACL,OAAO,EAAE;MACjB,IAAI,CAACA,OAAO,GAAGN,MAAM,CAACC,KAAK,CAAC,IAAI,CAACO,MAAM,CAAC;MACxC,IAAI,CAACT,IAAI,CAACa,IAAI,CAAC,IAAI,CAACN,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAACE,MAAM,CAAC;IACjD;IACA,OAAO,IAAI,CAACF,OAAO;EACrB;EAEAO,KAAKA,CAACC,QAAQ,EAAE;IACdA,QAAQ,GAAGA,QAAQ,IAAI,CAAC;IACxB,IAAI,CAACR,OAAO,GAAGC,SAAS;IACxB,IAAI,CAACF,MAAM,GAAGS,QAAQ;EACxB;EAEAC,KAAKA,CAACC,GAAG,EAAE;IACT,IAAId,IAAI,GAAG,IAAI,CAACH,IAAI,CAACS,MAAM,GAAG,CAAC;IAC/B,OAAON,IAAI,GAAGc,GAAG,EAAE;MACjBd,IAAI,IAAI,CAAC;IACX;IACA,MAAMe,GAAG,GAAGjB,MAAM,CAACC,KAAK,CAACC,IAAI,CAAC;IAC9B,IAAI,CAACH,IAAI,CAACa,IAAI,CAACK,GAAG,EAAE,CAAC,CAAC;IACtB,IAAI,CAAClB,IAAI,GAAGkB,GAAG;EACjB;EAEAC,OAAOA,CAACC,IAAI,EAAE;IACZ,IAAI,CAACb,OAAO,GAAGC,SAAS;IAExB,IAAIa,KAAK,GAAG,IAAI,CAACf,MAAM,GAAG,IAAI,CAACN,IAAI,CAACsB,KAAK,CAACF,IAAI,EAAE,IAAI,CAACd,MAAM,EAAE,IAAI,CAACF,SAAS,CAAC;;IAE5E;IACA,OAAOiB,KAAK,IAAI,IAAI,CAACrB,IAAI,CAACS,MAAM,GAAG,CAAC,EAAE;MACpC,IAAI,CAACO,KAAK,CAAC,IAAI,CAACV,MAAM,GAAGc,IAAI,CAACX,MAAM,CAAC;;MAErC;MACAY,KAAK,GAAG,IAAI,CAACf,MAAM,GAAG,IAAI,CAACN,IAAI,CAACsB,KAAK,CAACF,IAAI,EAAE,IAAI,CAACd,MAAM,EAAE,IAAI,CAACF,SAAS,CAAC;IAC1E;IAEA,IAAI,CAACE,MAAM,GAAGe,KAAK;EACrB;EAEAE,YAAYA,CAACC,KAAK,EAAE;IAClB,IAAIA,KAAK,CAACf,MAAM,EAAE;MAChB,IAAI,CAACF,OAAO,GAAGC,SAAS;MAExB,IAAI,IAAI,CAACC,MAAM,GAAGe,KAAK,CAACf,MAAM,GAAG,IAAI,CAACC,QAAQ,EAAE;QAC9C,IAAI,CAACM,KAAK,CAAC,IAAI,CAACP,MAAM,GAAGe,KAAK,CAACf,MAAM,CAAC;MACxC;MACA;MACAe,KAAK,CAACxB,IAAI,CAACa,IAAI,CAAC,IAAI,CAACb,IAAI,EAAE,IAAI,CAACM,MAAM,EAAE,CAAC,EAAEkB,KAAK,CAACf,MAAM,CAAC;MACxD,IAAI,CAACH,MAAM,IAAIkB,KAAK,CAACf,MAAM;IAC7B;EACF;AACF;AAEAgB,MAAM,CAACC,OAAO,GAAG7B,SAAS"}