{"version": 3, "sources": ["../../../src/dialects/db2/data-types.js"], "sourcesContent": ["'use strict';\n\nconst momentTz = require('moment-timezone');\nconst moment = require('moment');\n\nmodule.exports = BaseTypes => {\n  const warn = BaseTypes.ABSTRACT.warn.bind(undefined,\n    'https://www.ibm.com/support/knowledgecenter/SSEPGG_11.1.0/' +\n    'com.ibm.db2.luw.sql.ref.doc/doc/r0008478.html');\n\n  /**\n   * Removes unsupported Db2 options, i.e., LENGTH, UNSIGNED and ZEROFILL,\n   * for the integer data types.\n   *\n   * @param {object} dataType The base integer data type.\n   * @private\n   */\n  function removeUnsupportedIntegerOptions(dataType) {\n    if (dataType._length || dataType.options.length || dataType._unsigned || dataType._zerofill) {\n      warn(`Db2 does not support '${dataType.key}' with options. Plain '${dataType.key}' will be used instead.`);\n      dataType._length = undefined;\n      dataType.options.length = undefined;\n      dataType._unsigned = undefined;\n      dataType._zerofill = undefined;\n    }\n  }\n\n  /**\n   * types: [hex, ...]\n   *\n   * @see Data types and table columns: https://www.ibm.com/support/knowledgecenter/en/SSEPGG_11.1.0/com.ibm.db2.luw.admin.dbobj.doc/doc/c0055357.html \n   */\n\n  BaseTypes.DATE.types.db2 = ['TIMESTAMP'];\n  BaseTypes.STRING.types.db2 = ['VARCHAR'];\n  BaseTypes.CHAR.types.db2 = ['CHAR'];\n  BaseTypes.TEXT.types.db2 = ['VARCHAR', 'CLOB'];\n  BaseTypes.TINYINT.types.db2 = ['SMALLINT'];\n  BaseTypes.SMALLINT.types.db2 = ['SMALLINT'];\n  BaseTypes.MEDIUMINT.types.db2 = ['INTEGER'];\n  BaseTypes.INTEGER.types.db2 = ['INTEGER'];\n  BaseTypes.BIGINT.types.db2 = ['BIGINT'];\n  BaseTypes.FLOAT.types.db2 = ['DOUBLE', 'REAL', 'FLOAT'];\n  BaseTypes.TIME.types.db2 = ['TIME'];\n  BaseTypes.DATEONLY.types.db2 = ['DATE'];\n  BaseTypes.BOOLEAN.types.db2 = ['BOOLEAN', 'BOOL', 'SMALLINT', 'BIT'];\n  BaseTypes.BLOB.types.db2 = ['BLOB'];\n  BaseTypes.DECIMAL.types.db2 = ['DECIMAL'];\n  BaseTypes.UUID.types.db2 = ['CHAR () FOR BIT DATA'];\n  BaseTypes.ENUM.types.db2 = ['VARCHAR'];\n  BaseTypes.REAL.types.db2 = ['REAL'];\n  BaseTypes.DOUBLE.types.db2 = ['DOUBLE'];\n  BaseTypes.GEOMETRY.types.db2 = false;\n\n  class BLOB extends BaseTypes.BLOB {\n    toSql() {\n      if (this._length) {\n        if (this._length.toLowerCase() === 'tiny') { // tiny = 255 bytes\n          return 'BLOB(255)';\n        }\n        if (this._length.toLowerCase() === 'medium') { // medium = 16M\n          return 'BLOB(16M)';\n        }\n        if (this._length.toLowerCase() === 'long') { // long = 2GB\n          return 'BLOB(2G)';\n        }\n        return `BLOB(${ this._length })`;\n      }\n      return 'BLOB'; // 1MB\n    }\n    escape(blob) {\n      return `BLOB('${ blob.toString().replace(/'/g, \"''\") }')`;\n    }\n    _stringify(value) {\n      if (Buffer.isBuffer(value)) {\n        return `BLOB('${ value.toString().replace(/'/g, \"''\") }')`;\n      }\n      if (Array.isArray(value)) {\n        value = Buffer.from(value);\n      } else {\n        value = Buffer.from(value.toString());\n      }\n      const hex = value.toString('hex');\n      return this._hexify(hex);\n    }\n    _hexify(hex) {\n      return `x'${ hex }'`;\n    }\n  }\n\n  class STRING extends BaseTypes.STRING {\n    toSql() {\n      if (!this._binary) {\n        if (this._length <= 4000) {\n          return `VARCHAR(${ this._length })`;\n        }\n        return `CLOB(${ this._length })`;\n      }\n      if (this._length < 255) {\n        return `CHAR(${ this._length }) FOR BIT DATA`;\n      }\n      if (this._length <= 4000) {\n        return `VARCHAR(${ this._length }) FOR BIT DATA`;\n      }\n      return `BLOB(${ this._length })`;\n    }\n    _stringify(value, options) {\n      if (this._binary) {\n        return BLOB.prototype._hexify(value.toString('hex'));\n      }\n      return options.escape(value);\n    }\n    _bindParam(value, options) {\n      return options.bindParam(this._binary ? Buffer.from(value) : value);\n    }\n  }\n  STRING.prototype.escape = false;\n\n  class TEXT extends BaseTypes.TEXT {\n    toSql() {\n      let len = 0;\n      if (this._length) {\n        switch (this._length.toLowerCase()) {\n          case 'tiny':\n            len = 256; // tiny = 2^8\n            break;\n          case 'medium':\n            len = 8192; // medium = 2^13 = 8k\n            break;\n          case 'long':\n            len = 65536; // long = 64k\n            break;\n        }\n        if ( isNaN(this._length) ) {\n          this._length = 32672;\n        }\n        if (len > 0 ) { this._length = len; }\n      } else { this._length = 32672; }\n      if ( this._length > 32672 )\n      {\n        len = `CLOB(${ this._length })`;\n      }\n      else\n      {\n        len = `VARCHAR(${ this._length })`;\n      }\n      warn(`Db2 does not support TEXT datatype. ${len} will be used instead.`);\n      return len;\n    }\n  }\n\n  class BOOLEAN extends BaseTypes.BOOLEAN {\n    toSql() {\n      return 'BOOLEAN';\n    }\n    _sanitize(value) {\n      if (value !== null && value !== undefined) {\n        if (Buffer.isBuffer(value) && value.length === 1) {\n          // Bit fields are returned as buffers\n          value = value[0];\n        }\n\n        if (typeof value === 'string') {\n          // Only take action on valid boolean strings.\n          value = value === 'true' ? true : value === 'false' ? false : value;\n          value = value === '\\u0001' ? true : value === '\\u0000' ? false : value;\n\n        } else if (typeof value === 'number') {\n          // Only take action on valid boolean integers.\n          value = value === 1 ? true : value === 0 ? false : value;\n        }\n      }\n\n      return value;\n    }\n  }\n  BOOLEAN.parse = BOOLEAN.prototype._sanitize;\n\n  class UUID extends BaseTypes.UUID {\n    toSql() {\n      return 'CHAR(36) FOR BIT DATA';\n    }\n  }\n\n  class NOW extends BaseTypes.NOW {\n    toSql() {\n      return 'CURRENT TIME';\n    }\n  }\n\n  class DATE extends BaseTypes.DATE {\n    toSql() {\n      if (this._length < 0) { this._length = 0; }\n      if (this._length > 6) { this._length = 6; }\n      return `TIMESTAMP${ this._length ? `(${ this._length })` : ''}`;\n    }\n    _stringify(date, options) {\n      if (!moment.isMoment(date)) {\n        date = this._applyTimezone(date, options);\n      }\n\n      if (this._length > 0) {\n        let msec = '.';\n        for ( let i = 0; i < this._length && i < 6; i++ ) {\n          msec += 'S';\n        }\n        return date.format(`YYYY-MM-DD HH:mm:ss${msec}`);\n      }\n      return date.format('YYYY-MM-DD HH:mm:ss');\n    }\n    static parse(value) {\n      if (typeof value !== 'string') {\n        value = value.string();\n      }\n      if (value === null) {\n        return value;\n      }\n      value = new Date(momentTz.utc(value));\n      return value;\n    }\n  }\n\n  class DATEONLY extends BaseTypes.DATEONLY {\n    static parse(value) {\n      return momentTz(value).format('YYYY-MM-DD');\n    }\n  }\n\n  class INTEGER extends BaseTypes.INTEGER {\n    constructor(length) {\n      super(length);\n      removeUnsupportedIntegerOptions(this);\n    }\n  }\n\n  class TINYINT extends BaseTypes.TINYINT {\n    constructor(length) {\n      super(length);\n      removeUnsupportedIntegerOptions(this);\n    }\n  }\n\n  class SMALLINT extends BaseTypes.SMALLINT {\n    constructor(length) {\n      super(length);\n      removeUnsupportedIntegerOptions(this);\n    }\n  }\n\n  class BIGINT extends BaseTypes.BIGINT {\n    constructor(length) {\n      super(length);\n      removeUnsupportedIntegerOptions(this);\n    }\n  }\n\n  class REAL extends BaseTypes.REAL {\n    constructor(length, decimals) {\n      super(length, decimals);\n      // Db2 does not support any options for real\n      if (this._length || this.options.length || this._unsigned || this._zerofill) {\n        warn('Db2 does not support REAL with options. Plain `REAL` will be used instead.');\n        this._length = undefined;\n        this.options.length = undefined;\n        this._unsigned = undefined;\n        this._zerofill = undefined;\n      }\n    }\n  }\n\n  class FLOAT extends BaseTypes.FLOAT {\n    constructor(length, decimals) {\n      super(length, decimals);\n      // Db2 does only support lengths as option.\n      // Values between 1-24 result in 7 digits precision (4 bytes storage size)\n      // Values between 25-53 result in 15 digits precision (8 bytes size)\n      // If decimals are provided remove these and print a warning\n      if (this._decimals) {\n        warn('Db2 does not support Float with decimals. Plain `FLOAT` will be used instead.');\n        this._length = undefined;\n        this.options.length = undefined;\n      }\n      if (this._unsigned) {\n        warn('Db2 does not support Float unsigned. `UNSIGNED` was removed.');\n        this._unsigned = undefined;\n      }\n      if (this._zerofill) {\n        warn('Db2 does not support Float zerofill. `ZEROFILL` was removed.');\n        this._zerofill = undefined;\n      }\n    }\n  }\n\n  class ENUM extends BaseTypes.ENUM {\n    toSql() {\n      return 'VARCHAR(255)';\n    }\n  }\n\n  class DOUBLE extends BaseTypes.DOUBLE {\n    constructor(length, decimals) {\n      super(length, decimals);\n      // db2 does not support any parameters for double\n      if (this._length || this.options.length ||\n          this._unsigned || this._zerofill)\n      {\n        warn('db2 does not support DOUBLE with options. ' +\n             'Plain DOUBLE will be used instead.');\n        this._length = undefined;\n        this.options.length = undefined;\n        this._unsigned = undefined;\n        this._zerofill = undefined;\n      }\n    }\n    toSql() {\n      return 'DOUBLE';\n    }\n  }\n  DOUBLE.prototype.key = DOUBLE.key = 'DOUBLE';\n\n  return {\n    BLOB,\n    BOOLEAN,\n    ENUM,\n    STRING,\n    UUID,\n    DATE,\n    DATEONLY,\n    NOW,\n    TINYINT,\n    SMALLINT,\n    INTEGER,\n    DOUBLE,\n    'DOUBLE PRECISION': DOUBLE,\n    BIGINT,\n    REAL,\n    FLOAT,\n    TEXT\n  };\n};\n"], "mappings": ";AAEA,MAAM,WAAW,QAAQ;AACzB,MAAM,SAAS,QAAQ;AAEvB,OAAO,UAAU,eAAa;AAC5B,QAAM,OAAO,UAAU,SAAS,KAAK,KAAK,QACxC;AAUF,2CAAyC,UAAU;AACjD,QAAI,SAAS,WAAW,SAAS,QAAQ,UAAU,SAAS,aAAa,SAAS,WAAW;AAC3F,WAAK,yBAAyB,SAAS,6BAA6B,SAAS;AAC7E,eAAS,UAAU;AACnB,eAAS,QAAQ,SAAS;AAC1B,eAAS,YAAY;AACrB,eAAS,YAAY;AAAA;AAAA;AAUzB,YAAU,KAAK,MAAM,MAAM,CAAC;AAC5B,YAAU,OAAO,MAAM,MAAM,CAAC;AAC9B,YAAU,KAAK,MAAM,MAAM,CAAC;AAC5B,YAAU,KAAK,MAAM,MAAM,CAAC,WAAW;AACvC,YAAU,QAAQ,MAAM,MAAM,CAAC;AAC/B,YAAU,SAAS,MAAM,MAAM,CAAC;AAChC,YAAU,UAAU,MAAM,MAAM,CAAC;AACjC,YAAU,QAAQ,MAAM,MAAM,CAAC;AAC/B,YAAU,OAAO,MAAM,MAAM,CAAC;AAC9B,YAAU,MAAM,MAAM,MAAM,CAAC,UAAU,QAAQ;AAC/C,YAAU,KAAK,MAAM,MAAM,CAAC;AAC5B,YAAU,SAAS,MAAM,MAAM,CAAC;AAChC,YAAU,QAAQ,MAAM,MAAM,CAAC,WAAW,QAAQ,YAAY;AAC9D,YAAU,KAAK,MAAM,MAAM,CAAC;AAC5B,YAAU,QAAQ,MAAM,MAAM,CAAC;AAC/B,YAAU,KAAK,MAAM,MAAM,CAAC;AAC5B,YAAU,KAAK,MAAM,MAAM,CAAC;AAC5B,YAAU,KAAK,MAAM,MAAM,CAAC;AAC5B,YAAU,OAAO,MAAM,MAAM,CAAC;AAC9B,YAAU,SAAS,MAAM,MAAM;AAE/B,qBAAmB,UAAU,KAAK;AAAA,IAChC,QAAQ;AACN,UAAI,KAAK,SAAS;AAChB,YAAI,KAAK,QAAQ,kBAAkB,QAAQ;AACzC,iBAAO;AAAA;AAET,YAAI,KAAK,QAAQ,kBAAkB,UAAU;AAC3C,iBAAO;AAAA;AAET,YAAI,KAAK,QAAQ,kBAAkB,QAAQ;AACzC,iBAAO;AAAA;AAET,eAAO,QAAS,KAAK;AAAA;AAEvB,aAAO;AAAA;AAAA,IAET,OAAO,MAAM;AACX,aAAO,SAAU,KAAK,WAAW,QAAQ,MAAM;AAAA;AAAA,IAEjD,WAAW,OAAO;AAChB,UAAI,OAAO,SAAS,QAAQ;AAC1B,eAAO,SAAU,MAAM,WAAW,QAAQ,MAAM;AAAA;AAElD,UAAI,MAAM,QAAQ,QAAQ;AACxB,gBAAQ,OAAO,KAAK;AAAA,aACf;AACL,gBAAQ,OAAO,KAAK,MAAM;AAAA;AAE5B,YAAM,MAAM,MAAM,SAAS;AAC3B,aAAO,KAAK,QAAQ;AAAA;AAAA,IAEtB,QAAQ,KAAK;AACX,aAAO,KAAM;AAAA;AAAA;AAIjB,uBAAqB,UAAU,OAAO;AAAA,IACpC,QAAQ;AACN,UAAI,CAAC,KAAK,SAAS;AACjB,YAAI,KAAK,WAAW,KAAM;AACxB,iBAAO,WAAY,KAAK;AAAA;AAE1B,eAAO,QAAS,KAAK;AAAA;AAEvB,UAAI,KAAK,UAAU,KAAK;AACtB,eAAO,QAAS,KAAK;AAAA;AAEvB,UAAI,KAAK,WAAW,KAAM;AACxB,eAAO,WAAY,KAAK;AAAA;AAE1B,aAAO,QAAS,KAAK;AAAA;AAAA,IAEvB,WAAW,OAAO,SAAS;AACzB,UAAI,KAAK,SAAS;AAChB,eAAO,KAAK,UAAU,QAAQ,MAAM,SAAS;AAAA;AAE/C,aAAO,QAAQ,OAAO;AAAA;AAAA,IAExB,WAAW,OAAO,SAAS;AACzB,aAAO,QAAQ,UAAU,KAAK,UAAU,OAAO,KAAK,SAAS;AAAA;AAAA;AAGjE,SAAO,UAAU,SAAS;AAE1B,qBAAmB,UAAU,KAAK;AAAA,IAChC,QAAQ;AACN,UAAI,MAAM;AACV,UAAI,KAAK,SAAS;AAChB,gBAAQ,KAAK,QAAQ;AAAA,eACd;AACH,kBAAM;AACN;AAAA,eACG;AACH,kBAAM;AACN;AAAA,eACG;AACH,kBAAM;AACN;AAAA;AAEJ,YAAK,MAAM,KAAK,UAAW;AACzB,eAAK,UAAU;AAAA;AAEjB,YAAI,MAAM,GAAI;AAAE,eAAK,UAAU;AAAA;AAAA,aAC1B;AAAE,aAAK,UAAU;AAAA;AACxB,UAAK,KAAK,UAAU,OACpB;AACE,cAAM,QAAS,KAAK;AAAA,aAGtB;AACE,cAAM,WAAY,KAAK;AAAA;AAEzB,WAAK,uCAAuC;AAC5C,aAAO;AAAA;AAAA;AAIX,wBAAsB,UAAU,QAAQ;AAAA,IACtC,QAAQ;AACN,aAAO;AAAA;AAAA,IAET,UAAU,OAAO;AACf,UAAI,UAAU,QAAQ,UAAU,QAAW;AACzC,YAAI,OAAO,SAAS,UAAU,MAAM,WAAW,GAAG;AAEhD,kBAAQ,MAAM;AAAA;AAGhB,YAAI,OAAO,UAAU,UAAU;AAE7B,kBAAQ,UAAU,SAAS,OAAO,UAAU,UAAU,QAAQ;AAC9D,kBAAQ,UAAU,MAAW,OAAO,UAAU,OAAW,QAAQ;AAAA,mBAExD,OAAO,UAAU,UAAU;AAEpC,kBAAQ,UAAU,IAAI,OAAO,UAAU,IAAI,QAAQ;AAAA;AAAA;AAIvD,aAAO;AAAA;AAAA;AAGX,UAAQ,QAAQ,QAAQ,UAAU;AAElC,qBAAmB,UAAU,KAAK;AAAA,IAChC,QAAQ;AACN,aAAO;AAAA;AAAA;AAIX,oBAAkB,UAAU,IAAI;AAAA,IAC9B,QAAQ;AACN,aAAO;AAAA;AAAA;AAIX,qBAAmB,UAAU,KAAK;AAAA,IAChC,QAAQ;AACN,UAAI,KAAK,UAAU,GAAG;AAAE,aAAK,UAAU;AAAA;AACvC,UAAI,KAAK,UAAU,GAAG;AAAE,aAAK,UAAU;AAAA;AACvC,aAAO,YAAa,KAAK,UAAU,IAAK,KAAK,aAAc;AAAA;AAAA,IAE7D,WAAW,MAAM,SAAS;AACxB,UAAI,CAAC,OAAO,SAAS,OAAO;AAC1B,eAAO,KAAK,eAAe,MAAM;AAAA;AAGnC,UAAI,KAAK,UAAU,GAAG;AACpB,YAAI,OAAO;AACX,iBAAU,IAAI,GAAG,IAAI,KAAK,WAAW,IAAI,GAAG,KAAM;AAChD,kBAAQ;AAAA;AAEV,eAAO,KAAK,OAAO,sBAAsB;AAAA;AAE3C,aAAO,KAAK,OAAO;AAAA;AAAA,WAEd,MAAM,OAAO;AAClB,UAAI,OAAO,UAAU,UAAU;AAC7B,gBAAQ,MAAM;AAAA;AAEhB,UAAI,UAAU,MAAM;AAClB,eAAO;AAAA;AAET,cAAQ,IAAI,KAAK,SAAS,IAAI;AAC9B,aAAO;AAAA;AAAA;AAIX,yBAAuB,UAAU,SAAS;AAAA,WACjC,MAAM,OAAO;AAClB,aAAO,SAAS,OAAO,OAAO;AAAA;AAAA;AAIlC,wBAAsB,UAAU,QAAQ;AAAA,IACtC,YAAY,QAAQ;AAClB,YAAM;AACN,sCAAgC;AAAA;AAAA;AAIpC,wBAAsB,UAAU,QAAQ;AAAA,IACtC,YAAY,QAAQ;AAClB,YAAM;AACN,sCAAgC;AAAA;AAAA;AAIpC,yBAAuB,UAAU,SAAS;AAAA,IACxC,YAAY,QAAQ;AAClB,YAAM;AACN,sCAAgC;AAAA;AAAA;AAIpC,uBAAqB,UAAU,OAAO;AAAA,IACpC,YAAY,QAAQ;AAClB,YAAM;AACN,sCAAgC;AAAA;AAAA;AAIpC,qBAAmB,UAAU,KAAK;AAAA,IAChC,YAAY,QAAQ,UAAU;AAC5B,YAAM,QAAQ;AAEd,UAAI,KAAK,WAAW,KAAK,QAAQ,UAAU,KAAK,aAAa,KAAK,WAAW;AAC3E,aAAK;AACL,aAAK,UAAU;AACf,aAAK,QAAQ,SAAS;AACtB,aAAK,YAAY;AACjB,aAAK,YAAY;AAAA;AAAA;AAAA;AAKvB,sBAAoB,UAAU,MAAM;AAAA,IAClC,YAAY,QAAQ,UAAU;AAC5B,YAAM,QAAQ;AAKd,UAAI,KAAK,WAAW;AAClB,aAAK;AACL,aAAK,UAAU;AACf,aAAK,QAAQ,SAAS;AAAA;AAExB,UAAI,KAAK,WAAW;AAClB,aAAK;AACL,aAAK,YAAY;AAAA;AAEnB,UAAI,KAAK,WAAW;AAClB,aAAK;AACL,aAAK,YAAY;AAAA;AAAA;AAAA;AAKvB,qBAAmB,UAAU,KAAK;AAAA,IAChC,QAAQ;AACN,aAAO;AAAA;AAAA;AAIX,uBAAqB,UAAU,OAAO;AAAA,IACpC,YAAY,QAAQ,UAAU;AAC5B,YAAM,QAAQ;AAEd,UAAI,KAAK,WAAW,KAAK,QAAQ,UAC7B,KAAK,aAAa,KAAK,WAC3B;AACE,aAAK;AAEL,aAAK,UAAU;AACf,aAAK,QAAQ,SAAS;AACtB,aAAK,YAAY;AACjB,aAAK,YAAY;AAAA;AAAA;AAAA,IAGrB,QAAQ;AACN,aAAO;AAAA;AAAA;AAGX,SAAO,UAAU,MAAM,OAAO,MAAM;AAEpC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,oBAAoB;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA;", "names": []}