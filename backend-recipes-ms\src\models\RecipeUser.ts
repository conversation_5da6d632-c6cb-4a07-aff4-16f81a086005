import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

export enum RecipeUserStatus {
  active = "active",
  inactive = "inactive",
}

interface RecipeUserAttributes {
  recipe_id: number;
  user_id: number;
  status: RecipeUserStatus;
  organization_id?: string;
  created_by: number;
  updated_by: number;
  created_at?: Date;
  updated_at?: Date;
}

export class RecipeUser
  extends Model<RecipeUserAttributes, never>
  implements RecipeUserAttributes {
  recipe_id!: number;
  user_id!: number;
  status!: RecipeUserStatus;
  organization_id?: string;
  created_by!: number;
  updated_by!: number;
  created_at!: Date;
  updated_at!: Date;
}

RecipeUser.init(
  {
    recipe_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true,
      references: {
        model: "mo_recipe",
        key: "id",
      },
      onDelete: "CASCADE",
      onUpdate: "CASCADE",
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true,
      references: {
        model: "users",
        key: "id",
      },
      onDelete: "CASCADE",
      onUpdate: "CASCADE",
    },
    status: {
      type: DataTypes.ENUM(Object.values(RecipeUserStatus)),
      allowNull: false,
      defaultValue: RecipeUserStatus.active,
    },
    organization_id: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },
    created_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    updated_by: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
  },
  {
    sequelize,
    tableName: "mo_recipe_user",
    modelName: "RecipeUser",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    indexes: [
      {
        unique: true,
        fields: ["recipe_id", "user_id"],
        name: "unique_recipe_user_bookmark",
      },
      {
        fields: ["organization_id"],
        name: "idx_recipe_user_organization",
      },
      {
        fields: ["status"],
        name: "idx_recipe_user_status",
      },
      {
        fields: ["created_by"],
        name: "idx_recipe_user_created_by",
      },
      {
        fields: ["updated_by"],
        name: "idx_recipe_user_updated_by",
      },
    ],
  }
);

// Define associations
RecipeUser.associate = (models: any) => {
  // RecipeUser belongs to Recipe
  RecipeUser.belongsTo(models.Recipe, {
    foreignKey: "recipe_id",
    as: "recipe",
  });

  // RecipeUser belongs to User (user_id)
  RecipeUser.belongsTo(models.User, {
    foreignKey: "user_id",
    as: "user",
  });

  // RecipeUser belongs to User (created_by)
  RecipeUser.belongsTo(models.User, {
    foreignKey: "created_by",
    as: "creator",
  });

  // RecipeUser belongs to User (updated_by)
  RecipeUser.belongsTo(models.User, {
    foreignKey: "updated_by",
    as: "updater",
  });

  // RecipeUser belongs to RecipeMaster (through recipe)
  // Note: User assignments should typically point to the master recipe, not specific versions
  // Users are assigned to work on the recipe concept, not a specific version
  RecipeUser.belongsTo(models.RecipeMaster, {
    foreignKey: "recipe_id",
    targetKey: "current_published_version_id",
    as: "masterRecipe",
    constraints: false, // No direct FK constraint
  });
};

export default RecipeUser;
