{"version": 3, "file": "UserContext.js", "sourceRoot": "", "sources": ["../../../../../src/bidi/core/UserContext.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIH,kEAA0D;AAC1D,oDAA4C;AAC5C,4DAA0E;AAC1E,4DAAwE;AAIxE,6DAAqD;AAYrD;;GAEG;IACU,WAAW;sBAAS,8BAAY;;;;;;;;iBAAhC,WAAY,SAAQ,WAe/B;;;YAqFA,wKAAQ,OAAO,6DAGd;YAMD,kNAAM,qBAAqB,6DAqB1B;YAMD,qKAAM,MAAM,6DAQX;YAMD,iLAAM,UAAU,6DAef;YAMD,8KAAM,SAAS,6DAYd;YAMD,6LAAM,cAAc,6DAWnB;;;QAxLD,MAAM,CAAC,OAAO,GAAG,SAAkB,CAAC;QAEpC,MAAM,CAAC,MAAM,CAAC,OAAgB,EAAE,EAAU;YACxC,MAAM,OAAO,GAAG,IAAI,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;YAC7C,OAAO,CAAC,WAAW,EAAE,CAAC;YACtB,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,OAAO,GAxBI,mDAAW,CAwBL;QACjB,0CAA0C;QACjC,iBAAiB,GAAG,IAAI,GAAG,EAA2B,CAAC;QACvD,YAAY,GAAG,IAAI,+BAAe,EAAE,CAAC;QACrC,GAAG,CAAS;QACZ,OAAO,CAAU;QAE1B,YAAoB,OAAgB,EAAE,EAAU;YAC9C,KAAK,EAAE,CAAC;YAER,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;YACd,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACzB,CAAC;QAED,WAAW;YACT,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAC1C,IAAI,8BAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAC/B,CAAC;YACF,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAC,MAAM,EAAC,EAAE,EAAE;gBACzC,IAAI,CAAC,OAAO,CAAC,4BAA4B,MAAM,EAAE,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC;YACH,cAAc,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,EAAC,MAAM,EAAC,EAAE,EAAE;gBAC/C,IAAI,CAAC,OAAO,CAAC,4BAA4B,MAAM,EAAE,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC;YAEH,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAC1C,IAAI,8BAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAChC,CAAC;YACF,cAAc,CAAC,EAAE,CAAC,gCAAgC,EAAE,IAAI,CAAC,EAAE;gBACzD,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;oBAChB,OAAO;gBACT,CAAC;gBAED,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC;oBAClC,OAAO;gBACT,CAAC;gBAED,MAAM,eAAe,GAAG,oCAAe,CAAC,IAAI,CAC1C,IAAI,EACJ,SAAS,EACT,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,GAAG,EACR,IAAI,CAAC,cAAc,CACpB,CAAC;gBACF,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE,EAAE,eAAe,CAAC,CAAC;gBAEhE,MAAM,sBAAsB,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAClD,IAAI,8BAAY,CAAC,eAAe,CAAC,CAClC,CAAC;gBACF,sBAAsB,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;oBACvC,sBAAsB,CAAC,kBAAkB,EAAE,CAAC;oBAE5C,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;gBACpD,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAC,eAAe,EAAC,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,QAAQ;YACV,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;QAC9B,CAAC;QACD,IAAI,gBAAgB;YAClB,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC;QACzC,CAAC;QACD,IAAI,MAAM;YACR,OAAO,IAAI,CAAC,OAAO,KAAK,SAAS,CAAC;QACpC,CAAC;QACD,IAAI,QAAQ;YACV,OAAO,IAAI,CAAC,MAAM,CAAC;QACrB,CAAC;QACD,IAAI,EAAE;YACJ,OAAO,IAAI,CAAC,GAAG,CAAC;QAClB,CAAC;QAGO,OAAO,CAAC,MAAe;YAC7B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;YACtB,IAAI,CAAC,6BAAa,CAAC,EAAE,CAAC;QACxB,CAAC;QAMD,KAAK,CAAC,qBAAqB,CACzB,IAAqC,EACrC,UAAwC,EAAE;YAE1C,MAAM,EACJ,MAAM,EAAE,EAAC,OAAO,EAAE,SAAS,EAAC,GAC7B,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,wBAAwB,EAAE;gBACrD,IAAI;gBACJ,GAAG,OAAO;gBACV,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,EAAE,EAAE;gBAC9C,WAAW,EAAE,IAAI,CAAC,GAAG;aACtB,CAAC,CAAC;YAEH,MAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAC9D,IAAA,kBAAM,EACJ,eAAe,EACf,sFAAsF,CACvF,CAAC;YAEF,2DAA2D;YAC3D,OAAO,eAAe,CAAC;QACzB,CAAC;QAMD,KAAK,CAAC,MAAM;YACV,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,2BAA2B,EAAE;oBACpD,WAAW,EAAE,IAAI,CAAC,GAAG;iBACtB,CAAC,CAAC;YACL,CAAC;oBAAS,CAAC;gBACT,IAAI,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC;YAC/C,CAAC;QACH,CAAC;QAMD,KAAK,CAAC,UAAU,CACd,UAA6B,EAAE,EAC/B,eAAmC,SAAS;YAE5C,MAAM,EACJ,MAAM,EAAE,EAAC,OAAO,EAAC,GAClB,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,oBAAoB,EAAE;gBACjD,GAAG,OAAO;gBACV,SAAS,EAAE;oBACT,IAAI,EAAE,YAAY;oBAClB,WAAW,EAAE,IAAI,CAAC,GAAG;oBACrB,YAAY;iBACb;aACF,CAAC,CAAC;YACH,OAAO,OAAO,CAAC;QACjB,CAAC;QAMD,KAAK,CAAC,SAAS,CACb,MAAkC,EAClC,YAAqB;YAErB,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC5C,MAAM;gBACN,SAAS,EAAE;oBACT,IAAI,EAAE,YAAY;oBAClB,YAAY;oBACZ,WAAW,EAAE,IAAI,CAAC,EAAE;iBACrB;aACF,CAAC,CAAC;QACL,CAAC;QAMD,KAAK,CAAC,cAAc,CAClB,MAAc,EACd,UAAiD,EACjD,KAAuC;YAEvC,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,2BAA2B,EAAE;gBACpD,MAAM;gBACN,UAAU;gBACV,KAAK;gBACL,WAAW,EAAE,IAAI,CAAC,GAAG;aACtB,CAAC,CAAC;QACL,CAAC;QAEQ,yBAvGR,+BAAe,wCAMf,IAAA,+BAAe,EAAc,OAAO,CAAC,EAAE;gBACtC,wCAAwC;gBACxC,OAAO,OAAO,CAAC,OAAQ,CAAC;YAC1B,CAAC,CAAC,yBAwBD,IAAA,+BAAe,EAAc,OAAO,CAAC,EAAE;gBACtC,wCAAwC;gBACxC,OAAO,OAAO,CAAC,OAAQ,CAAC;YAC1B,CAAC,CAAC,6BAWD,IAAA,+BAAe,EAAc,OAAO,CAAC,EAAE;gBACtC,wCAAwC;gBACxC,OAAO,OAAO,CAAC,OAAQ,CAAC;YAC1B,CAAC,CAAC,4BAkBD,IAAA,+BAAe,EAAc,OAAO,CAAC,EAAE;gBACtC,wCAAwC;gBACxC,OAAO,OAAO,CAAC,OAAQ,CAAC;YAC1B,CAAC,CAAC,iCAeD,IAAA,+BAAe,EAAc,OAAO,CAAC,EAAE;gBACtC,wCAAwC;gBACxC,OAAO,OAAO,CAAC,OAAQ,CAAC;YAC1B,CAAC,CAAC,GAcQ,6BAAa,EAAC;YACtB,IAAI,CAAC,OAAO;gBACV,gFAAgF,CAAC;YACnF,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAC,CAAC,CAAC;YAE5C,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;YAC5B,KAAK,CAAC,6BAAa,CAAC,EAAE,CAAC;QACzB,CAAC;;;AAjNU,kCAAW"}